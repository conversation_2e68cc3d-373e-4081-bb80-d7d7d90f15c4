{"version": "2.0.0", "tasks": [{"label": "🎨 Agente TikZ: <PERSON><PERSON><PERSON>", "type": "shell", "command": "echo", "args": ["📸 IMAGEN SELECCIONADA: ${file}\n\n🤖 INSTRUCCIONES PARA AUGMENT:\n\n1. <PERSON><PERSON>za la imagen matemática seleccionada\n2. Identifica elementos geométricos, funciones, gráficas o figuras\n3. Genera código TikZ/pgfplots profesional para recrear la imagen\n4. Usa templates de ${workspaceFolder}/Auxiliares/Ejemplos-Funcionales-Rmd/Rnw/preferidos\n5. Optimiza para ejercicios ICFES tipo R-exams\n6. ⚠️ IMPORTANTE: Guarda SIEMPRE los archivos en:\n   ${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ/\n\n💡 AHORA USA AUGMENT PARA ANALIZAR LA IMAGEN Y GENERAR EL CÓDIGO TIKZ"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "problemMatcher": [], "detail": "Analiza imagen matemática con Augment IA y genera código TikZ"}, {"label": "🎨 Agente TikZ: <PERSON><PERSON><PERSON><PERSON> Personalizado", "type": "shell", "command": "echo", "args": ["📸 IMAGEN: ${file}\n🎯 PROMPT: ${input:promptPersonalizado}\n\n🤖 USA AUGMENT PARA:\n1. <PERSON><PERSON><PERSON> la imagen con el contexto específico\n2. <PERSON><PERSON> código TikZ optimizado\n3. <PERSON><PERSON><PERSON> ejercicio .Rnw completo si es necesario\n4. ⚠️ GUARDAR SIEMPRE EN: ${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ/\n\n💡 CONTINÚA CON AUGMENT IA"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "clear": true}, "problemMatcher": [], "detail": "Análisis personalizado con Augment IA"}, {"label": "📋 Compilar TikZ con LaTeX", "type": "shell", "command": "pdflatex", "args": ["-interaction=nonstopmode", "${file}"], "options": {"cwd": "${fileDirname}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": ["$latex"], "detail": "Compila archivo TikZ/LaTeX seleccionado"}, {"label": "🎨 <PERSON><PERSON><PERSON>", "type": "shell", "command": "qtikz", "args": ["${file}"], "options": {"cwd": "${fileDirname}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Abrir archivo TikZ en QTikz para visualización"}, {"label": "📁 <PERSON><PERSON><PERSON> Templates TikZ", "type": "shell", "command": "code", "args": ["${workspaceFolder}/Auxiliares/Ejemplos-Funcionales-Rmd/Rnw/preferidos"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "<PERSON><PERSON><PERSON> carpeta con templates TikZ de referencia"}, {"label": "🔍 Ver Laboratorio TikZ", "type": "shell", "command": "ls", "args": ["-la", "."], "options": {"cwd": "${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Ver archivos en el laboratorio TikZ"}], "inputs": [{"id": "prompt<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Contexto específico para análisis (ej: trigonometría, geometría, álgebra)", "default": "Analiza esta imagen matemática para crear un ejercicio ICFES con código TikZ profesional", "type": "promptString"}]}