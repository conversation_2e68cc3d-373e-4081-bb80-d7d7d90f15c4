% ==============================
% TEMPLATE ICFES - SECUENCIAS GEOMÉTRICAS
% ==============================
% Agente Graficador TikZ + Augment
% Versión: 2.0.0
% Uso: Ejercicios ICFES sobre secuencias de números triangulares, cuadrados, etc.

\begin{tikzpicture}[scale=1.5]

% ==============================
% CONFIGURACIÓN PERSONALIZABLE
% ==============================

% Parámetros del ejercicio
\def\numeroEjercicio{7}
\def\tipoSecuencia{triangular} % triangular, cuadrada, pentagonal
\def\numPosiciones{4}

% Datos de la secuencia (personalizar según ejercicio)
\def\datosSecuencia{{1,3,6,10}} % Números triangulares
\def\posicionPregunta{9}
\def\opcionA{45}
\def\opcionB{55}
\def\opcionC{56}
\def\opcionD{66}

% Colores educativos
\definecolor{puntoColor}{RGB}{139,69,19}    % Marrón para puntos
\definecolor{lineaColor}{RGB}{0,0,0}        % Negro para líneas
\definecolor{textoColor}{RGB}{0,0,0}        % Negro para texto
\definecolor{tablaColor}{RGB}{200,200,200}  % Gris claro para tabla

% ==============================
% TÍTULO DEL EJERCICIO
% ==============================
\node[above, font=\Large\bfseries] at (6, 5) 
  {\numeroEjercicio. En la siguiente figura se muestra la sucesión de números \tipoSecuencia es};

% ==============================
% FIGURAS DE LA SECUENCIA
% ==============================

% Posición 1: 1 punto
\node[below, font=\bfseries] at (1, 0.3) {Posición 1};
\fill[puntoColor] (1, 1) circle (4pt);

% Posición 2: 3 puntos en triángulo
\node[below, font=\bfseries] at (3.5, 0.3) {Posición 2};
% Base del triángulo (2 puntos)
\fill[puntoColor] (3, 1) circle (4pt);
\fill[puntoColor] (4, 1) circle (4pt);
% Vértice superior (1 punto)
\fill[puntoColor] (3.5, 1.6) circle (4pt);
% Líneas del triángulo
\draw[lineaColor, thick] (3, 1) -- (3.5, 1.6) -- (4, 1) -- cycle;

% Posición 3: 6 puntos en triángulo más grande
\node[below, font=\bfseries] at (6.5, 0.3) {Posición 3};
% Base del triángulo (3 puntos)
\fill[puntoColor] (5.5, 1) circle (4pt);
\fill[puntoColor] (6.5, 1) circle (4pt);
\fill[puntoColor] (7.5, 1) circle (4pt);
% Segunda fila (2 puntos)
\fill[puntoColor] (6, 1.5) circle (4pt);
\fill[puntoColor] (7, 1.5) circle (4pt);
% Vértice superior (1 punto)
\fill[puntoColor] (6.5, 2) circle (4pt);
% Líneas del triángulo
\draw[lineaColor, thick] (5.5, 1) -- (6.5, 2) -- (7.5, 1) -- cycle;
% Líneas internas
\draw[lineaColor, thick] (6, 1.5) -- (7, 1.5);
\draw[lineaColor, thick] (5.5, 1) -- (6, 1.5);
\draw[lineaColor, thick] (7.5, 1) -- (7, 1.5);

% Posición 4: 10 puntos en triángulo aún más grande
\node[below, font=\bfseries] at (10, 0.3) {Posición 4};
% Base del triángulo (4 puntos)
\fill[puntoColor] (8.5, 1) circle (4pt);
\fill[puntoColor] (9.5, 1) circle (4pt);
\fill[puntoColor] (10.5, 1) circle (4pt);
\fill[puntoColor] (11.5, 1) circle (4pt);
% Segunda fila (3 puntos)
\fill[puntoColor] (9, 1.5) circle (4pt);
\fill[puntoColor] (10, 1.5) circle (4pt);
\fill[puntoColor] (11, 1.5) circle (4pt);
% Tercera fila (2 puntos)
\fill[puntoColor] (9.5, 2) circle (4pt);
\fill[puntoColor] (10.5, 2) circle (4pt);
% Vértice superior (1 punto)
\fill[puntoColor] (10, 2.5) circle (4pt);
% Líneas del triángulo
\draw[lineaColor, thick] (8.5, 1) -- (10, 2.5) -- (11.5, 1) -- cycle;
% Líneas internas horizontales
\draw[lineaColor, thick] (9, 1.5) -- (11, 1.5);
\draw[lineaColor, thick] (9.5, 2) -- (10.5, 2);
% Líneas internas diagonales izquierda
\draw[lineaColor, thick] (8.5, 1) -- (9, 1.5);
\draw[lineaColor, thick] (9, 1.5) -- (9.5, 2);
% Líneas internas diagonales derecha
\draw[lineaColor, thick] (11.5, 1) -- (11, 1.5);
\draw[lineaColor, thick] (11, 1.5) -- (10.5, 2);

% Indicación de continuidad
\node[font=\Huge] at (13, 1.5) {$\cdots$};
\fill[puntoColor] (13.8, 1.2) circle (3pt);
\fill[puntoColor] (14.2, 1.2) circle (3pt);
\fill[puntoColor] (14.6, 1.2) circle (3pt);

% ==============================
% TABLA DE DATOS
% ==============================
\node[above, font=\large] at (7.5, -0.8) 
  {\textbf{La siguiente tabla muestra los primeros cuatro términos de la sucesión:}};

% Marco de la tabla
\draw[lineaColor, thick] (3, -2) rectangle (12, -4);

% Líneas verticales de la tabla
\draw[lineaColor, thick] (5, -2) -- (5, -4);
\draw[lineaColor, thick] (7, -2) -- (7, -4);
\draw[lineaColor, thick] (9, -2) -- (9, -4);
\draw[lineaColor, thick] (11, -2) -- (11, -4);

% Línea horizontal de separación
\draw[lineaColor, thick] (3, -3) -- (12, -3);

% Encabezados de la tabla
\node[font=\large\bfseries] at (4, -2.5) {Posición};
\node[font=\large\bfseries] at (6, -2.5) {1};
\node[font=\large\bfseries] at (8, -2.5) {2};
\node[font=\large\bfseries] at (10, -2.5) {3};
\node[font=\large\bfseries] at (11.5, -2.5) {4};

% Datos de la tabla
\node[font=\large\bfseries] at (4, -3.5) {Área (cm²)};
\node[font=\large\bfseries] at (6, -3.5) {1};
\node[font=\large\bfseries] at (8, -3.5) {3};
\node[font=\large\bfseries] at (10, -3.5) {6};
\node[font=\large\bfseries] at (11.5, -3.5) {10};

% ==============================
% PREGUNTA Y OPCIONES
% ==============================
\node[above, font=\large\bfseries] at (7.5, -5) 
  {La cantidad de puntos que tendría la figura \posicionPregunta\ es:};

% Opciones de respuesta en formato ICFES
\node[left, font=\large\bfseries] at (3, -6) {A. \opcionA};
\node[right, font=\large\bfseries] at (9, -6) {C. \opcionC};
\node[left, font=\large\bfseries] at (3, -6.8) {B. \opcionB};
\node[right, font=\large\bfseries] at (9, -6.8) {D. \opcionD};

% ==============================
% ELEMENTOS EDUCATIVOS ADICIONALES
% ==============================

% Flechas indicando progresión
\draw[->, thick, blue] (1.5, 1.8) -- (2.5, 1.8);
\draw[->, thick, blue] (4.5, 2.2) -- (5, 2.2);
\draw[->, thick, blue] (8, 2.7) -- (8.5, 2.7);

% Etiquetas de cantidad de puntos
\node[above, font=\small, blue] at (1, 1.5) {1 punto};
\node[above, font=\small, blue] at (3.5, 2.2) {3 puntos};
\node[above, font=\small, blue] at (6.5, 2.6) {6 puntos};
\node[above, font=\small, blue] at (10, 3.1) {10 puntos};

% Patrón matemático (opcional)
\node[right, font=\footnotesize, gray] at (13, -1) {
  \begin{tabular}{l}
    Patrón: $T(n) = \frac{n(n+1)}{2}$ \\
    $T(9) = \frac{9 \times 10}{2} = 45$
  \end{tabular}
};

\end{tikzpicture}

% ==============================
% NOTAS DE USO
% ==============================
% 1. Modificar \datosSecuencia según la secuencia específica
% 2. Cambiar \tipoSecuencia para otros tipos (cuadrada, pentagonal)
% 3. Ajustar \posicionPregunta y opciones según el ejercicio
% 4. Personalizar colores según preferencias institucionales
% 5. Escalar con scale= para diferentes tamaños de presentación

% EJEMPLOS DE CONFIGURACIÓN:
% Para números cuadrados: \def\datosSecuencia{{1,4,9,16}}
% Para números pentagonales: \def\datosSecuencia{{1,5,12,22}}
% Para Fibonacci: \def\datosSecuencia{{1,1,2,3,5,8}}
