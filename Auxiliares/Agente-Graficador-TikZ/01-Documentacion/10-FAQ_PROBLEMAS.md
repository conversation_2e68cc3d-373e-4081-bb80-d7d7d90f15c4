# ❓ FAQ - Preguntas Frecuentes y Solución de Problemas

## 🎯 **Guía Simplificada de Solución de Problemas**

Esta guía cubre los problemas comunes del **Agente TikZ Simplificado** que usa solo **Augment + VSCode**.

**🚀 ¡Buenas noticias! La mayoría de problemas de Python ya no existen.**

---

## ✅ **Problemas Eliminados en la Versión Simplificada**

### **🎉 Ya NO necesitas preocuparte por:**
- ❌ ~~Instalación de Python~~
- ❌ ~~Entornos virtuales (venv)~~
- ❌ ~~Dependencias pip~~
- ❌ ~~Errores de importación~~
- ❌ ~~Problemas de PATH~~
- ❌ ~~Versiones de Python~~

### **✨ Solo necesitas:**
- ✅ VSCode con Augment IA
- ✅ Tasks configurados (ya incluidos)
- ✅ ¡Listo para usar!

---

## 🔧 **Problemas Actuales Posibles**

### **❌ "No encuentro los tasks del Agente TikZ"**

#### **Síntomas:**
- No aparecen tasks con 🎨 en Command Palette
- "Tasks: Run Task" no muestra opciones del agente

#### **Solución:**
```
1. Verificar que estás en el directorio correcto del proyecto
2. Ctrl+Shift+P → "Developer: Reload Window"
3. Verificar archivo .vscode/tasks.json existe
4. Si no existe, copiar desde 03-Configuracion-VSCode/01-tasks.json
```

### **❌ "Variable ${file} can not be resolved"**

#### **Síntomas:**
- Error al ejecutar tasks del Agente TikZ
- Mensaje "Please open an editor"
- Tasks no funcionan

#### **Solución:**
```
OPCIÓN 1 - Usar task sin dependencias:
1. Ctrl+Shift+P → "Tasks: Run Task"
2. Seleccionar "🚀 Agente TikZ: Inicio Rápido"
3. Este task NO requiere archivo seleccionado

OPCIÓN 2 - Seleccionar imagen primero:
1. Abrir cualquier imagen en VSCode
2. Hacer clic en la imagen para activarla
3. Luego ejecutar el task normal

OPCIÓN 3 - Usar Augment directamente:
1. Abrir imagen en VSCode
2. Usar Augment IA directamente sin tasks
3. Pedirle que genere código TikZ
```
### **❌ "Augment IA no responde o no está disponible"**

#### **Síntomas:**
- Augment no analiza la imagen
- No se genera código TikZ
- Augment parece inactivo

#### **Solución:**
```
1. Verificar que Augment IA está instalado en VSCode
2. Verificar conexión a internet
3. Reiniciar VSCode si es necesario
4. Probar con una imagen más simple primero
```

### **❌ "No se guardan archivos en el Laboratorio"**

#### **Síntomas:**
- No aparecen archivos en Laboratorio_Agente_TikZ/
- Augment genera código pero no se guarda

#### **Solución:**
```
1. Verificar que la carpeta Laboratorio_Agente_TikZ/ existe
2. Crear manualmente si no existe:
   mkdir -p Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ/
3. Verificar permisos de escritura
4. Guardar manualmente el código generado por Augment
```

### **❌ "LaTeX no encontrado para compilación"**

#### **Síntomas:**
- "pdflatex: command not found"
- Tasks de compilación fallan
- No se generan PDFs

#### **Soluciones:**

**Linux (Ubuntu/Debian):**
```bash
sudo apt install texlive-latex-extra texlive-pictures
```

**macOS:**
```bash
brew install mactex
# O descargar MacTeX completo
```

**Windows:**
```
1. Descargar MiKTeX de miktex.org
2. O descargar TeX Live de tug.org
3. Instalar con configuración completa
```

**Verificar instalación:**
```bash
pdflatex --version
which pdflatex
```

---

## 🎨 **Problemas con VSCode**

### **❌ "Tasks no aparecen en Command Palette"**

#### **Síntomas:**
- No se ven tasks del agente
- "No tasks configured" en VSCode
- Command Palette vacío

#### **Diagnóstico:**
```bash
# Verificar archivo tasks.json
ls .vscode/tasks.json
cat .vscode/tasks.json | python3 -m json.tool
```

#### **Soluciones:**

**Recargar VSCode:**
```
Ctrl+Shift+P → "Developer: Reload Window"
```

**Reconfigurar automáticamente:**
```bash
cd Auxiliares/Agente-Graficador-TikZ/03-Configuracion-VSCode/
python3 05-setup_vscode.py
```

**Verificar sintaxis JSON:**
```bash
# Si hay errores de sintaxis
cd .vscode/
python3 -c "import json; json.load(open('tasks.json'))"
```

**Crear manualmente:**
```bash
mkdir -p .vscode
cp Auxiliares/Agente-Graficador-TikZ/03-Configuracion-VSCode/01-tasks.json .vscode/tasks.json
```

### **❌ "Terminal integrado no funciona"**

#### **Síntomas:**
- Terminal no se abre
- Comandos no se ejecutan
- Error de shell

#### **Soluciones:**

**Cambiar shell por defecto:**
```
Ctrl+Shift+P → "Terminal: Select Default Profile"
Seleccionar: bash, zsh, o cmd según sistema
```

**Reiniciar terminal:**
```
Ctrl+Shift+P → "Terminal: Kill All Terminals"
Ctrl+` → Abrir nuevo terminal
```

**Configurar manualmente:**
```json
// En .vscode/settings.json
{
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.defaultProfile.windows": "Command Prompt",
  "terminal.integrated.defaultProfile.osx": "zsh"
}
```

### **❌ "Extensiones no funcionan"**

#### **Síntomas:**
- LaTeX Workshop no compila
- Python extension no reconoce código
- Snippets no aparecen

#### **Soluciones:**

**Instalar extensiones requeridas:**
```
1. Ctrl+Shift+X (Extensions)
2. Buscar e instalar:
   - LaTeX Workshop
   - Python
   - TikZ (opcional)
```

**Recargar extensiones:**
```
Ctrl+Shift+P → "Developer: Reload Window"
```

**Verificar configuración:**
```json
// En .vscode/settings.json
{
  "python.defaultInterpreterPath": "python3",
  "latex-workshop.latex.autoBuild.run": "onSave"
}
```

---

## 🖼️ **Problemas con Procesamiento de Imágenes**

### **❌ "No se generan archivos de salida"**

#### **Síntomas:**
- Agente se ejecuta pero no crea archivos
- Directorio tikz_generado/ vacío
- No hay errores visibles

#### **Diagnóstico:**
```bash
# Verificar permisos
ls -la tikz_generado/
ls -la .

# Verificar espacio en disco
df -h

# Verificar imagen válida
file mi_imagen.png
```

#### **Soluciones:**

**Crear directorio:**
```bash
mkdir -p tikz_generado
chmod 755 tikz_generado
```

**Verificar permisos:**
```bash
chmod +w tikz_generado/
chmod +x Auxiliares/Agente-Graficador-TikZ/02-Codigo-Agente/01-agente_principal.py
```

**Probar manualmente:**
```bash
cd Auxiliares/Agente-Graficador-TikZ/02-Codigo-Agente/
python3 01-agente_principal.py ../04-Ejemplos-y-Pruebas/01-imagen_ejemplo.png
```

### **❌ "Error procesando imagen"**

#### **Síntomas:**
- "Error loading image"
- "Invalid image format"
- Agente falla inmediatamente

#### **Soluciones:**

**Verificar formato de imagen:**
```bash
file mi_imagen.png
# Debe mostrar: PNG image data, JPEG image data, etc.
```

**Convertir formato si es necesario:**
```bash
# Convertir a PNG
convert mi_imagen.jpg mi_imagen.png

# O usar herramientas online
```

**Verificar tamaño de imagen:**
```bash
identify mi_imagen.png
# Verificar que no sea demasiado grande (>10MB)
```

**Probar con imagen de ejemplo:**
```bash
cp Auxiliares/Agente-Graficador-TikZ/04-Ejemplos-y-Pruebas/01-imagen_ejemplo.png .
# Procesar imagen de ejemplo conocida
```

### **❌ "Análisis incorrecto o de baja calidad"**

#### **Síntomas:**
- Código TikZ no se parece a la imagen
- Elementos importantes faltantes
- Baja confianza en análisis

#### **Soluciones:**

**Mejorar calidad de imagen:**
```
1. Aumentar contraste
2. Usar fondo blanco con líneas oscuras
3. Aumentar resolución (mínimo 800x600)
4. Eliminar ruido y artefactos
```

**Usar prompt personalizado:**
```
1. Task: "Análisis Personalizado"
2. Prompt específico: "Analiza esta función cuadrática con especial atención al vértice y intersecciones"
```

**Ajustar configuración:**
```json
// En 02-Codigo-Agente/05-configuracion.json
{
  "configuracion_analisis": {
    "max_iteraciones": 10,
    "umbral_similitud": 0.98,
    "resolucion_analisis": [1200, 900]
  }
}
```

---

## 🎨 **Problemas con QTikz**

### **❌ "LaTeX Error: Can be used only in preamble"**

#### **Síntomas:**
- Error al abrir archivo en QTikz
- Mensaje sobre \documentclass

#### **Solución:**
```
✅ USAR ARCHIVO CORRECTO:
- Para QTikz: nombre_qtikz.tikz
- Para LaTeX: nombre_agente.tikz

❌ NO usar archivo *_agente.tikz en QTikz
✅ SÍ usar archivo *_qtikz.tikz en QTikz
```

### **❌ "QTikz no compila el código"**

#### **Síntomas:**
- Código no se renderiza
- Errores de compilación en QTikz

#### **Soluciones:**

**Verificar QTikz instalado:**
```bash
qtikz --version
# Si no está instalado:
# Ubuntu: sudo apt install qtikz
# macOS: brew install --cask qtikz
```

**Probar código mínimo:**
```latex
\begin{tikzpicture}
\draw (0,0) -- (1,1);
\end{tikzpicture}
```

**Verificar configuración QTikz:**
```
Edit → Preferences:
- Compiler: pdflatex
- Options: -interaction=nonstopmode
```

---

## 📄 **Problemas con LaTeX**

### **❌ "Package tikz not found"**

#### **Síntomas:**
- Error compilando archivos TikZ
- "File 'tikz.sty' not found"

#### **Soluciones:**

**Instalar paquetes completos:**
```bash
# Ubuntu/Debian
sudo apt install texlive-pictures texlive-latex-extra

# macOS (con MacTeX completo)
# Ya incluido

# Windows (MiKTeX)
# Instalar paquetes automáticamente o manualmente
```

**Actualizar paquetes:**
```bash
tlmgr update --all
tlmgr install tikz pgfplots
```

### **❌ "Compilation timeout"**

#### **Síntomas:**
- LaTeX se cuelga compilando
- Proceso no termina

#### **Soluciones:**

**Simplificar código:**
```
1. Reducir número de samples en plot
2. Simplificar curvas complejas
3. Eliminar elementos redundantes
```

**Aumentar timeout:**
```json
// En 05-configuracion.json
{
  "configuracion_validacion": {
    "timeout_compilacion": 60
  }
}
```

---

## 🔍 **Debugging Avanzado**

### **Logs Detallados**

#### **Habilitar logging completo:**
```json
// En 02-Codigo-Agente/05-configuracion.json
{
  "configuracion_logging": {
    "level": "DEBUG",
    "mostrar_progreso_detallado": true,
    "guardar_archivos_temporales": true
  }
}
```

#### **Ejecutar con logs:**
```bash
cd Auxiliares/Agente-Graficador-TikZ/02-Codigo-Agente/
python3 01-agente_principal.py imagen.png 2>&1 | tee debug.log
```

### **Tests de Diagnóstico**

#### **Ejecutar tests completos:**
```bash
cd Auxiliares/Agente-Graficador-TikZ/04-Ejemplos-y-Pruebas/
python3 05-test_agente.py
```

#### **Test individual:**
```python
# Test específico
def test_imagen_especifica():
    resultado = procesar_imagen("mi_imagen_problema.png")
    print(f"Resultado: {resultado}")
    return resultado
```

---

## 📊 **Problemas de Rendimiento**

### **❌ "Procesamiento muy lento"**

#### **Síntomas:**
- Agente tarda más de 2 minutos
- VSCode se congela
- Sistema se ralentiza

#### **Soluciones:**

**Reducir configuración:**
```json
{
  "configuracion_analisis": {
    "resolucion_analisis": [400, 300],
    "max_iteraciones": 3,
    "timeout_procesamiento": 15
  }
}
```

**Cerrar aplicaciones:**
```
1. Cerrar navegadores web
2. Cerrar aplicaciones pesadas
3. Liberar memoria RAM
```

**Procesar imágenes más pequeñas:**
```bash
# Redimensionar imagen
convert imagen_grande.png -resize 800x600 imagen_pequena.png
```

### **❌ "Memoria insuficiente"**

#### **Síntomas:**
- Error "MemoryError"
- Sistema se queda sin memoria

#### **Soluciones:**

**Configuración de memoria:**
```json
{
  "configuracion_analisis": {
    "resolucion_analisis": [400, 300]
  }
}
```

**Aumentar memoria virtual:**
```bash
# Linux: aumentar swap
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

---

## 🆘 **Obtener Ayuda Adicional**

### **Información para Reportar Problemas**

#### **Información del sistema:**
```bash
# Información básica
python3 --version
pdflatex --version
qtikz --version
code --version

# Información del agente
cd Auxiliares/Agente-Graficador-TikZ/04-Ejemplos-y-Pruebas/
python3 05-test_agente.py > sistema_info.txt
```

#### **Logs relevantes:**
```bash
# Logs del agente
ls logs/agente_*.log

# Logs de VSCode
# Linux: ~/.config/Code/logs/
# macOS: ~/Library/Application Support/Code/logs/
# Windows: %APPDATA%\Code\logs\
```

### **Checklist de Verificación Completa**

#### **✅ Sistema Base:**
- [ ] Python 3.8+ instalado y en PATH
- [ ] LaTeX (TeX Live/MiKTeX) instalado
- [ ] VSCode instalado y funcionando
- [ ] Permisos de escritura en directorio

#### **✅ Dependencias Python:**
- [ ] opencv-python instalado
- [ ] numpy instalado
- [ ] Pillow instalado
- [ ] Todas las dependencias en requirements.txt

#### **✅ Configuración VSCode:**
- [ ] .vscode/tasks.json existe y es válido
- [ ] Extensiones requeridas instaladas
- [ ] Terminal integrado funciona
- [ ] Python interpreter configurado

#### **✅ Estructura de Archivos:**
- [ ] Agente en Auxiliares/Agente-Graficador-TikZ/
- [ ] Directorio tikz_generado/ existe
- [ ] Permisos de escritura correctos
- [ ] Archivos de configuración presentes

#### **✅ Funcionalidad:**
- [ ] Tests automatizados pasan
- [ ] Imagen de ejemplo se procesa
- [ ] Archivos TikZ se generan
- [ ] QTikz abre archivos sin errores

---

## 🎯 **Prevención de Problemas**

### **Mejores Prácticas**

#### **Mantenimiento regular:**
```bash
# Actualizar dependencias
pip install --upgrade -r 06-requirements.txt

# Limpiar archivos temporales
rm -rf logs/*.log
rm -rf tikz_generado/*.tmp

# Verificar funcionamiento
python3 05-test_agente.py
```

#### **Backup de configuración:**
```bash
# Respaldar configuración
cp -r .vscode/ .vscode_backup/
cp 02-Codigo-Agente/05-configuracion.json config_backup.json
```

### **Monitoreo de Salud**

#### **Script de verificación:**
```python
# En 04-Ejemplos-y-Pruebas/11-health_check.py
def verificar_salud_agente():
    """Verificar salud general del agente."""
    
    checks = {
        "python": verificar_python(),
        "dependencias": verificar_dependencias(),
        "latex": verificar_latex(),
        "vscode": verificar_vscode(),
        "agente": verificar_agente()
    }
    
    return checks
```

---

## 🎉 **¡Problemas Resueltos!**

**Esta guía cubre el 99% de los problemas comunes del Agente TikZ.**

### **Si tu problema no está aquí:**
1. **Ejecutar tests:** `python3 05-test_agente.py`
2. **Revisar logs:** Archivos en `logs/`
3. **Verificar configuración:** Usar checklist completo
4. **Probar con imagen simple:** Usar ejemplo incluido

### **La mayoría de problemas se resuelven con:**
- ✅ **Reconfiguración automática:** `python3 05-setup_vscode.py`
- ✅ **Reinstalación de dependencias:** `pip install -r 06-requirements.txt`
- ✅ **Recarga de VSCode:** `Ctrl+Shift+P → Developer: Reload Window`

**¡El agente está diseñado para ser robusto y fácil de reparar!** 🛠️✨
