# ⚡ Configuración Súper Rápida del Agente TikZ

## 🎯 **Configuración en 2 Minutos (¡Sin instalaciones!)**

### **📋 Prerequisitos Súper Simples**
- ✅ **VSCode** con **Augment IA** instalado
- ✅ **LaTeX** instalado (solo para compilar PDFs - opcional)
- ⚠️ **QTikz/KTikz** (opcional, para testing visual)

**🚀 ¡Eso es todo! No necesitas Python ni nada más.**

---

## ✅ **Paso 1: Verificar que tienes lo básico (30 segundos)**

### **Verificar VSCode + Augment:**
```
1. Abrir VSCode
2. Verificar que Augment IA está instalado y funcionando
3. ¡Listo!
```

### **Verificar LaTeX (opcional):**
```bash
pdflatex --version
# Solo necesario si quieres compilar PDFs
```

### **Si falta LaTeX (opcional):**
```bash
# Ubuntu/Debian
sudo apt install texlive-latex-extra

# macOS (con Homebrew)
brew install mactex

# Windows
# Descargar e instalar MiKTeX desde miktex.org
```

---

## ✅ **Paso 2: ¡Ya está listo! (30 segundos)**

### **Verificar que los tasks están disponibles:**
```
1. Abrir VSCode en el directorio del proyecto
2. Ctrl+Shift+P
3. Escribir "Tasks: Run Task"
4. Verificar que aparecen tasks con 🎨
```

### **Si no aparecen los tasks:**
```
1. Verificar que estás en el directorio correcto
2. Ctrl+Shift+P → "Developer: Reload Window"
3. Los tasks deberían aparecer automáticamente
```

---

## 🎨 **Paso 3: Probar el Agente (1 minuto)**

### **Hacer tu primera prueba:**
```
1. Abrir cualquier imagen matemática en VSCode
2. Ctrl+Shift+P → "Tasks: Run Task"
3. Seleccionar "🎨 Agente TikZ: Analizar Imagen Matemática"
4. Usar Augment IA para generar código TikZ
5. ¡Listo!
```

### **Verificar resultados:**
```
📁 Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ/
├── imagen_agente.tikz     # Código TikZ generado
└── imagen_analisis.json   # Análisis completo
```
📂 Directorio VSCode: /ruta/al/proyecto/.vscode
📋 Copiando archivos de configuración:
  ✅ tasks.json
  ✅ tikz.code-snippets
  ✅ settings.json
  ✅ keybindings.json
📁 Creando directorios:
  ✅ tikz_generado/
  ✅ logs/
🔍 Verificando dependencias:
  ✅ python3
  ✅ pdflatex
  ✅ convert
✅ CONFIGURACIÓN COMPLETADA
🚀 VSCode está listo para usar el Agente TikZ
```

---

## 🧪 **Paso 4: Probar Funcionamiento (1 minuto)**

### **Ejecutar tests automatizados:**
```bash
cd ../04-Ejemplos-y-Pruebas/
python3 05-test_agente.py
```

### **Resultado esperado:**
```
🧪 SUITE DE TESTS - AGENTE TIKZ + AUGMENT
==================================================
🧪 TEST 1: Configuración Básica
✅ Python 3.8+ disponible
✅ cv2 disponible
✅ numpy disponible
✅ PIL disponible
✅ ../02-Codigo-Agente/01-agente_principal.py existe
✅ ../03-Configuracion-VSCode/01-tasks.json existe
✅ LaTeX disponible
⚠️ QTikz (opcional) no disponible (opcional)
✅ TEST 1 EXITOSO: Configuración básica OK

🧪 TEST 2: Procesamiento de Imagen
🔄 Ejecutando agente...
✅ Agente ejecutado exitosamente
✅ 01-imagen_ejemplo_qtikz.tikz generado
✅ 01-imagen_ejemplo_agente.tikz generado
✅ 01-imagen_ejemplo_analisis.json generado
✅ TEST 2 EXITOSO: Procesamiento completado

📊 RESUMEN DE TESTS
==================================================
Test 1: ✅ EXITOSO
Test 2: ✅ EXITOSO
Test 3: ✅ EXITOSO
Test 4: ✅ EXITOSO
Test 5: ✅ EXITOSO

Resultado: 5/5 tests exitosos
🎉 ¡TODOS LOS TESTS EXITOSOS!
✅ El Agente TikZ está completamente funcional
```

---

## 🎨 **Paso 5: Primera Prueba Real (30 segundos)**

### **Abrir VSCode en el proyecto:**
```bash
cd ../../..
code .
```

### **Procesar imagen de ejemplo:**
```
1. En VSCode: File → Open → 04-Ejemplos-y-Pruebas/01-imagen_ejemplo.png
2. Presionar: Ctrl+Shift+P
3. Escribir: "Tasks: Run Task"
4. Seleccionar: "🎨 Agente TikZ + Augment: Analizar Imagen"
5. Esperar 30 segundos
6. Ver resultado en: tikz_generado/
```

### **Verificar resultado en QTikz (opcional):**
```bash
qtikz tikz_generado/01-imagen_ejemplo_qtikz.tikz
```

---

## ✅ **Verificación de Instalación Exitosa**

### **Archivos que deben existir:**
```
✅ .vscode/tasks.json                    # Tasks configurados
✅ .vscode/tikz.code-snippets           # Snippets TikZ
✅ tikz_generado/                       # Directorio de salida
✅ logs/                                # Directorio de logs
```

### **Funcionalidades que deben funcionar:**
```
✅ Tasks de VSCode aparecen en Command Palette
✅ Agente procesa imágenes sin errores
✅ Se generan archivos TikZ válidos
✅ QTikz abre archivos *_qtikz.tikz sin errores
✅ LaTeX compila archivos *_agente.tikz correctamente
```

---

## 🆘 **Solución de Problemas Comunes**

### **❌ "python3: command not found"**
```bash
# Verificar instalación Python
which python3
python --version

# Si no está instalado:
# Ubuntu/Debian: sudo apt install python3
# macOS: brew install python
# Windows: Descargar de python.org
```

### **❌ "pip install falla"**
```bash
# Actualizar pip
python3 -m pip install --upgrade pip

# Instalar con usuario
pip install --user -r 06-requirements.txt

# Si persiste, instalar individualmente:
pip install opencv-python numpy Pillow
```

### **❌ "Tasks no aparecen en VSCode"**
```bash
# Verificar archivo tasks.json
ls .vscode/tasks.json

# Recargar VSCode
# Ctrl+Shift+P → "Developer: Reload Window"

# Reconfigurar si es necesario
cd Auxiliares/Agente-Graficador-TikZ/03-Configuracion-VSCode/
python3 05-setup_vscode.py
```

### **❌ "pdflatex not found"**
```bash
# Ubuntu/Debian
sudo apt install texlive-latex-extra texlive-pictures

# macOS
brew install mactex

# Windows
# Descargar MiKTeX de miktex.org
```

### **❌ "QTikz no abre archivos"**
```
Solución: Usar archivo *_qtikz.tikz (no *_agente.tikz)
Los archivos *_qtikz.tikz son específicos para QTikz
Los archivos *_agente.tikz son para LaTeX completo
```

---

## 🎯 **Próximos Pasos**

### **Después de la instalación exitosa:**
```
1. 📖 Leer: 03-TUTORIAL_BASICO.md (procesar tu primera imagen)
2. 📖 Revisar: 05-REFERENCIA_VSCODE.md (flujo de trabajo diario)
3. 📖 Explorar: 07-TEMPLATES_GUIA.md (personalizar templates)
4. 🎨 Practicar: Procesar tus propias imágenes matemáticas
```

### **Para uso avanzado:**
```
1. 📖 Estudiar: 04-TUTORIAL_AVANZADO.md
2. 📖 Personalizar: 08-PERSONALIZACION.md
3. 📖 Contribuir: 09-MEJORAS_FUTURAS.md
```

---

## 🎉 **¡Instalación Completada!**

**El Agente Graficador TikZ + Augment + VSCode está listo para convertir tus imágenes matemáticas en código TikZ profesional.**

### **Tiempo total de instalación:** ⏱️ **5 minutos**
### **Estado:** ✅ **Completamente funcional**
### **Próximo paso:** 📖 **`03-TUTORIAL_BASICO.md`**

**¡Empieza a crear código TikZ profesional desde tus imágenes!** 🎨✨
