# 🎨 Tutorial Básico: Tu Primera Imagen → Código TikZ

## 🎯 **Objetivo: Procesar tu primera imagen en 3 pasos súper simples**

Al final de este tutorial habrás convertido una imagen matemática en código TikZ profesional usando **solo Augment IA dentro de VSCode**.

**🚀 Sin Python, sin entornos virtuales, sin complicaciones.**

---

## 📋 **Prerequisitos (Súper Simples)**
- ✅ VSCode con Augment IA instalado
- ✅ Proyecto abierto en VSCode
- ✅ Una imagen matemática para procesar (.png, .jpg, .jpeg)
- ✅ ¡Eso es todo! No necesitas nada más

---

## 🚀 **Método 1: Usando Tasks de VSCode (Recomendado)**

### **Paso 1: Preparar tu imagen**

#### **Opción A: Usar imagen de ejemplo incluida**
```
📁 Auxiliares/Agente-Graficador-TikZ/04-Ejemplos-y-Pruebas/01-imagen_ejemplo.png
```

#### **Opción B: Usar tu propia imagen**
```
1. Copiar tu imagen al directorio del proyecto
2. Formatos soportados: .png, .jpg, .jpeg
3. Resolución recomendada: mínimo 800x600
4. Contenido: función matemática, figura geométrica, o diagrama
```

#### **Consejos para mejores resultados:**
- ✅ **Buen contraste:** Líneas oscuras sobre fondo claro
- ✅ **Elementos claros:** Ejes, curvas y puntos bien definidos
- ✅ **Sin ruido:** Imagen limpia sin artefactos
- ✅ **Tamaño adecuado:** No demasiado pequeña ni muy grande

### **Paso 2: Abrir imagen en VSCode**

#### **Abrir la imagen:**
```
1. En VSCode: File → Open (Ctrl+O)
2. Navegar a tu imagen
3. Seleccionar y abrir
4. La imagen se mostrará en el editor
```

#### **Verificar que la imagen está activa:**
```
- La pestaña de la imagen debe estar seleccionada
- Puedes ver la imagen en el editor principal
- El nombre del archivo aparece en la barra de título
```

### **Paso 3: Ejecutar el Agente TikZ Simplificado**

#### **Abrir Command Palette:**
```
Presionar: Ctrl+Shift+P (Windows/Linux) o Cmd+Shift+P (Mac)
```

#### **Buscar task del agente:**
```
Escribir: "Tasks: Run Task"
Presionar: Enter
```

#### **Seleccionar el agente:**
```
Buscar: "🎨 Agente TikZ: Analizar Imagen Matemática"
Seleccionar y presionar: Enter
```

#### **Usar Augment IA:**
```
📋 El task mostrará las instrucciones
🤖 Usa Augment IA para analizar la imagen
✨ Augment generará el código TikZ automáticamente
⚡ ¡Instantáneo! Sin esperas ni procesamiento
```

### **Paso 4: Ver resultados en el Laboratorio**

#### **Archivos generados automáticamente en:**
```
📁 Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ/
├── nombre_agente.tikz     # Código TikZ profesional
└── nombre_analisis.json   # Análisis completo con Augment
```

#### **Verificar resultados:**
```
✅ GENERACIÓN COMPLETADA CON AUGMENT
📄 Código TikZ: Laboratorio_Agente_TikZ/imagen_agente.tikz
📋 Análisis: Laboratorio_Agente_TikZ/imagen_analisis.json
```

---

## 🎨 **Verificar Resultado en QTikz**

### **Abrir QTikz:**
```bash
qtikz
# o
ktikz
```

### **Cargar archivo generado:**
```
1. En QTikz: File → Open
2. Navegar a: Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ/
3. Seleccionar: nombre_agente.tikz
4. Abrir
```

### **Verificar renderizado:**
```
✅ La gráfica se renderiza sin errores
✅ Los elementos principales están presentes
✅ Los colores y estilos son apropiados
✅ La escala es correcta
```

---

## 📄 **Usar Código en LaTeX**

### **Para documentos académicos:**
```latex
\documentclass{article}
\usepackage{tikz}
\usepackage{pgfplots}

\begin{document}

% Incluir código TikZ generado
\input{Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ/nombre_agente.tikz}

\end{document}
```

### **Compilar documento:**
```bash
pdflatex mi_documento.tex
```

---

## 🔄 **Método 2: Usando Terminal Directo**

### **Para usuarios avanzados:**
```bash
# Navegar al agente
cd Auxiliares/Agente-Graficador-TikZ/02-Codigo-Agente/

# Ejecutar agente directamente
python3 01-agente_principal.py ruta/a/tu/imagen.png

# Con prompt personalizado
python3 01-agente_principal.py imagen.png "Analiza esta función cuadrática con especial atención al vértice"
```

---

## 📊 **Entender el Análisis Generado**

### **Archivo de metadatos (nombre_analisis.json):**
```json
{
  "imagen_original": "mi_imagen.png",
  "timestamp": "2025-01-13_14:30:25",
  "analisis_augment": {
    "tipo_grafica": "funcion_matematica",
    "elementos_detectados": ["ejes", "curva_principal", "puntos"],
    "confianza": 0.92
  },
  "archivo_tikz": "tikz_generado/mi_imagen_agente.tikz",
  "archivo_qtikz": "tikz_generado/mi_imagen_qtikz.tikz",
  "lineas_codigo": 45
}
```

### **Información útil:**
- **tipo_grafica:** Tipo detectado por Augment IA
- **elementos_detectados:** Componentes identificados
- **confianza:** Nivel de confianza del análisis (0-1)
- **lineas_codigo:** Complejidad del código generado

---

## 🎯 **Ejemplo Completo: Función Cuadrática**

### **Imagen de entrada:**
```
Función: y = 0.5x² - 1
Elementos: Ejes coordenados, parábola, vértice marcado
```

### **Código TikZ generado:**
```latex
\begin{tikzpicture}[scale=1.2]

% Cuadrícula de fondo sutil
\draw[gray!20, thin] (-4,-3) grid[step=0.5] (4,3);

% Ejes coordenados principales
\draw[thick, ->] (-4,0) -- (4,0) node[below right] {$x$};
\draw[thick, ->] (0,-3) -- (0,3) node[above left] {$y$};

% Función cuadrática detectada
\draw[blue, very thick] plot[domain=-3:3, samples=50] (\x, {0.5*\x^2 - 1});

% Vértice marcado
\fill[red] (0,-1) circle (3pt) node[below right] {Vértice};

% Marcas en ejes
\foreach \x in {-3,-2,-1,1,2,3}
  \draw (\x,-0.1) -- (\x,0.1) node[below] {\x};
\foreach \y in {-2,-1,1,2}
  \draw (-0.1,\y) -- (0.1,\y) node[left] {\y};

% Origen
\fill[black] (0,0) circle (1.5pt);

\end{tikzpicture}
```

### **Resultado en QTikz:**
```
✅ Parábola renderizada correctamente
✅ Vértice en (0, -1) marcado en rojo
✅ Ejes con marcas numéricas
✅ Cuadrícula de fondo sutil
✅ Escala apropiada
```

---

## 🔧 **Personalizar el Análisis**

### **Usar prompt personalizado:**
```
1. Ctrl+Shift+P → "Tasks: Run Task"
2. Seleccionar: "🎨 Agente TikZ: Análisis Personalizado"
3. Ingresar prompt específico
```

### **Ejemplos de prompts útiles:**

#### **Para funciones:**
```
"Analiza esta función trigonométrica. Identifica amplitud, período y fase. Genera código TikZ con marcas específicas en puntos importantes."
```

#### **Para geometría:**
```
"Analiza esta figura geométrica. Identifica ángulos, medidas y relaciones. Genera código TikZ con construcciones precisas y etiquetas."
```

#### **Para diagramas:**
```
"Analiza este diagrama de flujo. Identifica nodos, conexiones y jerarquía. Genera código TikZ optimizado para presentaciones."
```

---

## 🆘 **Solución de Problemas Comunes**

### **❌ "Task no aparece"**
```
Solución:
1. Verificar que la imagen está abierta y seleccionada
2. Recargar VSCode: Ctrl+Shift+P → "Developer: Reload Window"
3. Verificar configuración: ls .vscode/tasks.json
```

### **❌ "No se generan archivos"**
```
Solución:
1. Verificar permisos: ls -la tikz_generado/
2. Crear directorio: mkdir -p tikz_generado
3. Verificar imagen válida: file mi_imagen.png
```

### **❌ "Error en QTikz"**
```
Solución:
1. Usar archivo correcto: *_qtikz.tikz (NO *_agente.tikz)
2. Verificar QTikz instalado: qtikz --version
3. Probar con imagen de ejemplo incluida
```

### **❌ "Código TikZ incorrecto"**
```
Solución:
1. Verificar calidad de imagen (contraste, resolución)
2. Usar prompt más específico
3. Editar código manualmente si es necesario
```

---

## 🎯 **Próximos Pasos**

### **Después de tu primera imagen exitosa:**
```
1. 📖 Explorar: 05-REFERENCIA_VSCODE.md (flujo de trabajo diario)
2. 📖 Aprender: 04-TUTORIAL_AVANZADO.md (funciones avanzadas)
3. 📖 Personalizar: 07-TEMPLATES_GUIA.md (modificar templates)
4. 🎨 Practicar: Procesar diferentes tipos de imágenes
```

### **Para mejorar resultados:**
```
1. Experimentar con diferentes prompts
2. Ajustar configuración en 02-Codigo-Agente/05-configuracion.json
3. Personalizar templates en 05-Templates-TikZ/
4. Combinar código generado con edición manual
```

---

## 🎉 **¡Felicitaciones!**

**Has completado exitosamente tu primer procesamiento de imagen con el Agente TikZ + Augment IA.**

### **Lo que has logrado:**
- ✅ **Convertir imagen** en código TikZ profesional
- ✅ **Usar Augment IA** para análisis inteligente
- ✅ **Integrar con VSCode** para flujo eficiente
- ✅ **Verificar en QTikz** compatibilidad sin errores
- ✅ **Generar código LaTeX** listo para publicación

### **Siguiente nivel:** 📖 **`04-TUTORIAL_AVANZADO.md`**

**¡Ahora puedes convertir cualquier imagen matemática en código TikZ profesional!** 🎨✨
