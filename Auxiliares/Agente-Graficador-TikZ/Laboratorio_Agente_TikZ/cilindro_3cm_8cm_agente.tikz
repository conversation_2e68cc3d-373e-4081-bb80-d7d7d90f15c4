% 🎨 Código TikZ generado por Agente TikZ + Augment
% Imagen: Cilindro con radio 3cm y altura 8cm
% Fecha: 2025-01-14
% Ubicación: Laboratorio_Agente_TikZ

\begin{tikzpicture}[scale=1.0, thick, >=stealth]
    
    % === PARÁMETROS DEL CILINDRO ===
    \def\radiovis{1.5}      % Radio visual (3cm escalado 1:2)
    \def\alturavis{4}       % Altura visual (8cm escalado 1:2)
    \def\perspectiva{0.4}   % Factor de perspectiva para elipses
    
    % === BASE INFERIOR ===
    % Elipse completa (visible)
    \draw[thick] (0,0) ellipse (\radiovis and \radiovis*\perspectiva);
    
    % === PAREDES LATERALES ===
    % Líneas laterales visibles
    \draw[thick] (\radiovis,0) -- (\radiovis,\alturavis);
    \draw[thick] (-\radiovis,0) -- (-\radiovis,\alturavis);
    
    % === BASE SUPERIOR ===
    % Elipse superior (visible)
    \draw[thick] (0,\alturavis) ellipse (\radiovis and \radiovis*\perspectiva);
    
    % === LÍNEAS OCULTAS ===
    % Parte trasera de la base inferior (punteada)
    \draw[thick, dashed] (-\radiovis,0) arc (180:360:\radiovis and \radiovis*\perspectiva);
    
    % === ETIQUETAS DE DIMENSIONES ===
    % Flecha y etiqueta para el radio
    \draw[<->] (0,-0.8) -- (\radiovis,-0.8);
    \node[below] at (\radiovis/2,-0.8) {\textbf{3 cm}};
    
    % Flecha y etiqueta para la altura
    \draw[<->] (\radiovis+0.3,0) -- (\radiovis+0.3,\alturavis);
    \node[right] at (\radiovis+0.5,\alturavis/2) {\textbf{8 cm}};
    
    % === ELEMENTOS ADICIONALES ===
    % Puntos centrales para claridad
    \fill (0,0) circle (0.05);
    \fill (0,\alturavis) circle (0.05);
    
    % Línea central (eje del cilindro) - opcional
    \draw[thin, gray, dashed] (0,0) -- (0,\alturavis);
    
\end{tikzpicture}

% === INFORMACIÓN ADICIONAL ===
% Volumen: V = π × r² × h = π × 3² × 8 = 72π ≈ 226.19 cm³
% Área lateral: A_lat = 2πrh = 2π × 3 × 8 = 48π ≈ 150.80 cm²
% Área total: A_total = 2πr² + 2πrh = 18π + 48π = 66π ≈ 207.35 cm²
