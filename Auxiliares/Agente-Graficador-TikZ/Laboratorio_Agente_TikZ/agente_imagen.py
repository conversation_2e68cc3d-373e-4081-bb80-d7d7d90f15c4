#!/usr/bin/env python3
"""
🎨 Wrapper del Agente TikZ para VSCode Tasks
===========================================

Este archivo actúa como punto de entrada para los tasks de VSCode,
redirigiendo al agente principal ubicado en Auxiliares/Agente-Graficador-TikZ/
"""

import sys
import os
from pathlib import Path

def main():
    """Función principal que redirige al agente real."""
    
    print("🎨 AGENTE TIKZ + AUGMENT - WRAPPER VSCODE")
    print("=" * 50)
    
    # Verificar argumentos
    if len(sys.argv) < 2:
        print("❌ Error: Se requiere especificar una imagen")
        print("📝 Uso: python agente_imagen.py <ruta_imagen> [prompt_personalizado]")
        sys.exit(1)
    
    # Obtener argumentos
    ruta_imagen = sys.argv[1]
    prompt_personalizado = sys.argv[2] if len(sys.argv) > 2 else ""
    
    print(f"📷 Imagen: {ruta_imagen}")
    if prompt_personalizado:
        print(f"💭 Prompt personalizado: {prompt_personalizado}")
    
    # Verificar que la imagen existe
    if not Path(ruta_imagen).exists():
        print(f"❌ Error: Imagen no encontrada: {ruta_imagen}")
        print("💡 Asegúrate de que la imagen esté abierta y seleccionada en VSCode")
        sys.exit(1)
    
    # Crear directorios necesarios
    Path("tikz_generado").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    print("📁 Directorios de salida verificados")
    
    # Ruta al agente principal
    agente_principal = Path("Auxiliares/Agente-Graficador-TikZ/02-Codigo-Agente/01-agente_principal.py")
    
    if not agente_principal.exists():
        print(f"❌ Error: Agente principal no encontrado en {agente_principal}")
        print("💡 Verifica que el proyecto esté completo")
        sys.exit(1)
    
    print(f"🔧 Ejecutando agente principal: {agente_principal}")
    print("-" * 50)
    
    # Importar y ejecutar el agente principal
    try:
        # Agregar el directorio del agente al path
        sys.path.insert(0, str(agente_principal.parent))
        
        # Importar el módulo del agente
        import importlib.util
        spec = importlib.util.spec_from_file_location("agente_principal", agente_principal)
        agente_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(agente_module)
        
        # Crear instancia del agente
        agente = agente_module.AgenteTikZImagen()
        
        # Procesar la imagen
        resultado = agente.procesar_imagen_completa(ruta_imagen, prompt_personalizado)
        
        # Mostrar resultado
        if resultado:
            agente.mostrar_resultado(resultado)
            print("\n✅ PROCESAMIENTO COMPLETADO EXITOSAMENTE")
            print("🎯 Archivos generados en: tikz_generado/")
            print("💡 Usa QTikz para ver el archivo *_qtikz.tikz")
        else:
            print("\n❌ ERROR EN EL PROCESAMIENTO")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Error ejecutando agente: {e}")
        print("💡 Verifica que todas las dependencias estén instaladas")
        sys.exit(1)

if __name__ == "__main__":
    main()
