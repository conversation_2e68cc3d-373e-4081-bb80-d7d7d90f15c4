% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =
% CÓDIGO TIKZ GENERADO POR AGENTE MEJORADO
% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =% =
% Timestamp: 2025-07-13T19:16:21.128814

\documentclass{standalone}
\usepackage{tikz}
\usepackage{pgfplots}
\usetikzlibrary{calc,arrows.meta,positioning}
\pgfplotsset{compat=1.18}

\begin{document}
\begin{tikzpicture}[scale=1.2]

% Cuadrícula de fondo sutil
\draw[gray!20, thin] (-4,-3) grid[step=0.5] (4,3);

% Ejes coordenados principales
\draw[thick, ->] (-4,0) -- (4,0) node[below right] {$x$};
\draw[thick, ->] (0,-3) -- (0,3) node[above left] {$y$};

% Función principal (por_determinar_visualmente)
% Adaptada del análisis de imagen
\draw[blue, very thick] plot[domain=-3:3, samples=50] (\x, {0.5*\x^2 - 1});

% Puntos importantes marcados
\fill[red] (0,0) circle (3pt) node[below right] {Origen};
\fill[blue] (-2,1) circle (2pt);
\fill[blue] (2,1) circle (2pt);

% Marcas y etiquetas en ejes
\foreach \x in {-3,-2,-1,1,2,3}
  \draw (\x,-0.1) -- (\x,0.1) node[below] {\x};
\foreach \y in {-2,-1,1,2}
  \draw (-0.1,\y) -- (0.1,\y) node[left] {\y};

% Origen del sistema coordenado
\fill[black] (0,0) circle (1.5pt);

\end{tikzpicture}
\end{document}