% ==============================
% CÓDIGO TIKZ CORREGIDO PARA EJERCICIO ICFES
% ==============================
% Sucesión de números triangulares - Ejercicio 7
% Corregido manualmente para representar correctamente las figuras triangulares

\begin{tikzpicture}[scale=1.0]

% Título del ejercicio
\node[above] at (6, 4.5) {\textbf{7. Sucesión de números triangulares}};

% Posición 1: 1 punto
\node[below] at (1, 0.5) {\textbf{Posición 1}};
\fill[brown] (1, 1) circle (3pt);

% Posición 2: 3 puntos en triángulo
\node[below] at (3, 0.5) {\textbf{Posición 2}};
% Triángulo con 3 puntos
\fill[brown] (2.5, 1) circle (3pt);
\fill[brown] (3, 1.5) circle (3pt);
\fill[brown] (3.5, 1) circle (3pt);
% Líneas del triángulo
\draw[thick] (2.5, 1) -- (3, 1.5) -- (3.5, 1) -- cycle;

% Posición 3: 6 puntos en triángulo más grande
\node[below] at (5.5, 0.5) {\textbf{Posición 3}};
% Base del triángulo (3 puntos)
\fill[brown] (4.5, 1) circle (3pt);
\fill[brown] (5, 1) circle (3pt);
\fill[brown] (5.5, 1) circle (3pt);
% Segunda fila (2 puntos)
\fill[brown] (4.75, 1.4) circle (3pt);
\fill[brown] (5.25, 1.4) circle (3pt);
% Vértice superior (1 punto)
\fill[brown] (5, 1.8) circle (3pt);
% Líneas del triángulo
\draw[thick] (4.5, 1) -- (5, 1.8) -- (5.5, 1) -- cycle;
% Líneas internas
\draw[thick] (4.75, 1.4) -- (5.25, 1.4);
\draw[thick] (4.5, 1) -- (4.75, 1.4);
\draw[thick] (5.5, 1) -- (5.25, 1.4);

% Posición 4: 10 puntos en triángulo aún más grande
\node[below] at (8.5, 0.5) {\textbf{Posición 4}};
% Base del triángulo (4 puntos)
\fill[brown] (7.5, 1) circle (3pt);
\fill[brown] (8, 1) circle (3pt);
\fill[brown] (8.5, 1) circle (3pt);
\fill[brown] (9, 1) circle (3pt);
% Segunda fila (3 puntos)
\fill[brown] (7.75, 1.4) circle (3pt);
\fill[brown] (8.25, 1.4) circle (3pt);
\fill[brown] (8.75, 1.4) circle (3pt);
% Tercera fila (2 puntos)
\fill[brown] (8, 1.8) circle (3pt);
\fill[brown] (8.5, 1.8) circle (3pt);
% Vértice superior (1 punto)
\fill[brown] (8.25, 2.2) circle (3pt);
% Líneas del triángulo
\draw[thick] (7.5, 1) -- (8.25, 2.2) -- (9, 1) -- cycle;
% Líneas internas horizontales
\draw[thick] (7.75, 1.4) -- (8.75, 1.4);
\draw[thick] (8, 1.8) -- (8.5, 1.8);
% Líneas internas diagonales izquierda
\draw[thick] (7.5, 1) -- (7.75, 1.4);
\draw[thick] (7.75, 1.4) -- (8, 1.8);
% Líneas internas diagonales derecha
\draw[thick] (9, 1) -- (8.75, 1.4);
\draw[thick] (8.75, 1.4) -- (8.5, 1.8);

% Indicación de continuidad
\node at (10.5, 1.5) {\Large $\cdots$};
\fill[brown] (11, 1.2) circle (3pt);
\fill[brown] (11.3, 1.2) circle (3pt);
\fill[brown] (11.6, 1.2) circle (3pt);

% Tabla de datos
\node[above] at (6, -1) {\textbf{La siguiente tabla muestra los primeros cuatro términos de la sucesión:}};

% Crear tabla
\draw[thick] (3, -2) rectangle (9, -3.5);
% Líneas verticales
\draw[thick] (4.5, -2) -- (4.5, -3.5);
\draw[thick] (6, -2) -- (6, -3.5);
\draw[thick] (7.5, -2) -- (7.5, -3.5);
% Línea horizontal
\draw[thick] (3, -2.75) -- (9, -2.75);

% Contenido de la tabla
\node at (3.75, -2.375) {\textbf{Posición}};
\node at (5.25, -2.375) {\textbf{1}};
\node at (6.75, -2.375) {\textbf{2}};
\node at (8.25, -2.375) {\textbf{3}};

\node at (3.75, -3.125) {\textbf{Área (cm²)}};
\node at (5.25, -3.125) {\textbf{1}};
\node at (6.75, -3.125) {\textbf{3}};
\node at (8.25, -3.125) {\textbf{6}};

% Agregar columna 4
\draw[thick] (9, -2) -- (10.5, -2) -- (10.5, -3.5) -- (9, -3.5);
\draw[thick] (9, -2.75) -- (10.5, -2.75);
\node at (9.75, -2.375) {\textbf{4}};
\node at (9.75, -3.125) {\textbf{10}};

% Pregunta
\node[above] at (6, -4.5) {\textbf{La cantidad de puntos que tendría la figura 9 es:}};

% Opciones de respuesta
\node[left] at (2, -5.5) {\textbf{A. 45}};
\node[right] at (6, -5.5) {\textbf{C. 56}};
\node[left] at (2, -6) {\textbf{B. 55}};
\node[right] at (6, -6) {\textbf{D. 66}};

\end{tikzpicture}
