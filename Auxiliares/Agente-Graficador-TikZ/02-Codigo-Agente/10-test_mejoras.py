#!/usr/bin/env python3
"""
🧪 Test de Mejoras del Agente TikZ v2.0
======================================
Verifica que todas las mejoras implementadas funcionan correctamente.
"""

import os
import sys
from pathlib import Path
import json

def test_modulos_especializados():
    """Test de módulos especializados."""
    print("🧪 TEST 1: MÓDULOS ESPECIALIZADOS")
    print("-" * 40)
    
    modulos = [
        "06-detector_contexto.py",
        "07-analizador_secuencias.py", 
        "08-prompts_especializados.py",
        "09-generador_tikz_especializado.py"
    ]
    
    for modulo in modulos:
        if Path(modulo).exists():
            print(f"✅ {modulo} - Presente")
        else:
            print(f"❌ {modulo} - Faltante")
    
    print()

def test_templates_especializados():
    """Test de templates especializados."""
    print("🧪 TEST 2: TEMPLATES ESPECIALIZADOS")
    print("-" * 40)
    
    templates_dir = Path("../05-Templates-TikZ")
    templates = [
        "09-template_icfes_secuencias.tikz"
    ]
    
    for template in templates:
        template_path = templates_dir / template
        if template_path.exists():
            print(f"✅ {template} - Presente")
        else:
            print(f"❌ {template} - Faltante")
    
    print()

def test_configuracion_actualizada():
    """Test de configuración actualizada."""
    print("🧪 TEST 3: CONFIGURACIÓN ACTUALIZADA")
    print("-" * 40)
    
    config_path = Path("05-configuracion.json")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        version = config.get("agente_info", {}).get("version", "N/A")
        mejoras = config.get("agente_info", {}).get("mejoras_v2", [])
        
        print(f"✅ Configuración presente - Versión: {version}")
        print(f"📋 Mejoras v2.0: {len(mejoras)} implementadas")
        
        for mejora in mejoras:
            print(f"   • {mejora}")
    else:
        print("❌ Configuración no encontrada")
    
    print()

def test_agente_mejorado():
    """Test del agente principal mejorado."""
    print("🧪 TEST 4: AGENTE PRINCIPAL MEJORADO")
    print("-" * 40)
    
    agente_path = Path("01-agente_principal_mejorado.py")
    if agente_path.exists():
        print("✅ Agente mejorado presente")
        
        # Verificar contenido clave
        with open(agente_path, 'r', encoding='utf-8') as f:
            contenido = f.read()
        
        caracteristicas = [
            "detector_contexto",
            "analizador_secuencias", 
            "prompts_especializados",
            "generador_tikz_especializado",
            "procesar_imagen_especializada"
        ]
        
        for caracteristica in caracteristicas:
            if caracteristica in contenido:
                print(f"   ✅ {caracteristica} - Integrado")
            else:
                print(f"   ❌ {caracteristica} - No encontrado")
    else:
        print("❌ Agente mejorado no encontrado")
    
    print()

def test_resultados_generados():
    """Test de resultados generados."""
    print("🧪 TEST 5: RESULTADOS GENERADOS")
    print("-" * 40)
    
    tikz_dir = Path("tikz_generado")
    if tikz_dir.exists():
        archivos = list(tikz_dir.glob("ejercicio_triangular_icfes*"))
        
        tipos_esperados = ["_agente.tikz", "_qtikz.tikz", "_analisis.json"]
        
        for tipo in tipos_esperados:
            archivos_tipo = [f for f in archivos if str(f).endswith(tipo)]
            if archivos_tipo:
                print(f"✅ Archivo {tipo} - Generado")
                
                # Verificar contenido básico
                archivo = archivos_tipo[0]
                with open(archivo, 'r', encoding='utf-8') as f:
                    contenido = f.read()
                
                if tipo == "_analisis.json":
                    try:
                        data = json.loads(contenido)
                        print(f"   📊 Metadatos: {len(data)} campos")
                    except:
                        print(f"   ⚠️ JSON inválido")
                else:
                    lineas = len(contenido.split('\n'))
                    print(f"   📝 Líneas: {lineas}")
            else:
                print(f"❌ Archivo {tipo} - No generado")
    else:
        print("❌ Directorio tikz_generado no existe")
    
    print()

def test_compatibilidad_qtikz():
    """Test de compatibilidad con QTikz."""
    print("🧪 TEST 6: COMPATIBILIDAD QTIKZ")
    print("-" * 40)
    
    qtikz_file = Path("tikz_generado/ejercicio_triangular_icfes_qtikz.tikz")
    if qtikz_file.exists():
        with open(qtikz_file, 'r', encoding='utf-8') as f:
            contenido = f.read()
        
        # Verificar que NO tiene preámbulo LaTeX
        problemas_qtikz = [
            "\\documentclass",
            "\\usepackage",
            "\\begin{document}",
            "\\end{document}"
        ]
        
        tiene_problemas = False
        for problema in problemas_qtikz:
            if problema in contenido:
                print(f"❌ Contiene {problema} (incompatible con QTikz)")
                tiene_problemas = True
        
        if not tiene_problemas:
            print("✅ Compatible con QTikz (sin preámbulo LaTeX)")
        
        # Verificar que SÍ tiene tikzpicture
        if "\\begin{tikzpicture}" in contenido and "\\end{tikzpicture}" in contenido:
            print("✅ Contiene código TikZ válido")
        else:
            print("❌ No contiene código TikZ válido")
    else:
        print("❌ Archivo QTikz no encontrado")
    
    print()

def mostrar_resumen():
    """Mostrar resumen de las mejoras implementadas."""
    print("📋 RESUMEN DE MEJORAS IMPLEMENTADAS")
    print("=" * 50)
    
    mejoras = [
        "✅ Detector de contexto educativo",
        "✅ Analizador de secuencias geométricas",
        "✅ Sistema de prompts especializados",
        "✅ Generador de código TikZ específico",
        "✅ Templates para ejercicios ICFES",
        "✅ Agente principal mejorado",
        "✅ Configuración actualizada v2.0",
        "✅ Compatibilidad QTikz mejorada"
    ]
    
    for mejora in mejoras:
        print(mejora)
    
    print("\n🎯 BENEFICIOS OBTENIDOS:")
    beneficios = [
        "• Reconocimiento automático de ejercicios ICFES",
        "• Análisis especializado de números triangulares",
        "• Código TikZ específico para contexto educativo",
        "• Prompts adaptativos según tipo de ejercicio",
        "• Mejor precisión en representación de secuencias",
        "• Compatibilidad perfecta con QTikz/KTikz",
        "• Metadatos detallados para análisis posterior"
    ]
    
    for beneficio in beneficios:
        print(beneficio)
    
    print("\n🚀 PRÓXIMOS PASOS:")
    print("• Instalar dependencias: pip install -r 06-requirements.txt")
    print("• Probar con más ejercicios ICFES")
    print("• Ajustar templates según necesidades específicas")
    print("• Integrar OCR para extracción de texto completa")

def main():
    """Ejecutar todos los tests."""
    print("🎨 AGENTE TIKZ v2.0 - VERIFICACIÓN DE MEJORAS")
    print("=" * 60)
    print()
    
    # Ejecutar tests
    test_modulos_especializados()
    test_templates_especializados()
    test_configuracion_actualizada()
    test_agente_mejorado()
    test_resultados_generados()
    test_compatibilidad_qtikz()
    
    # Mostrar resumen
    mostrar_resumen()
    
    print("\n🎉 VERIFICACIÓN COMPLETADA")
    print("El Agente TikZ v2.0 está listo con todas las mejoras implementadas!")

if __name__ == "__main__":
    main()
