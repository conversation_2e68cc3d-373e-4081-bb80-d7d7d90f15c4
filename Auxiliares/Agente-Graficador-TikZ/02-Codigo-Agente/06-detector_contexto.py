#!/usr/bin/env python3
"""
Detector de Contexto Educativo para Agente TikZ
===============================================
Detecta automáticamente el tipo de ejercicio y contexto educativo
para aplicar análisis especializado.
"""

import cv2
import numpy as np
import re
from typing import Dict, List, Tuple, Optional
import pytesseract
from pathlib import Path

class DetectorContextoEducativo:
    """Detector de contexto educativo para imágenes matemáticas."""
    
    def __init__(self):
        self.patrones_icfes = [
            r'\d+\.\s*En\s+la\s+siguiente',
            r'[ABCD]\.\s*\d+',
            r'La\s+cantidad\s+de\s+puntos',
            r'Posición\s+\d+',
            r'figura\s+\d+\s+es:'
        ]
        
        self.patrones_secuencias = [
            r'sucesión',
            r'secuencia',
            r'progresión',
            r'término',
            r'posición\s+\d+'
        ]
        
        self.patrones_geometria = [
            r'triángulo',
            r'cuadrado',
            r'círculo',
            r'figura\s+geométrica',
            r'área\s*\(cm²\)',
            r'perímetro'
        ]
    
    def detectar_contexto(self, imagen_path: str) -> Dict:
        """Detectar contexto principal de la imagen."""
        
        # Cargar imagen
        imagen = cv2.imread(imagen_path)
        if imagen is None:
            return {"tipo": "general", "confianza": 0.0}
        
        # Extraer texto con OCR
        texto = self._extraer_texto(imagen)
        
        # Analizar contexto
        contextos = {
            "ejercicio_icfes": self._es_ejercicio_icfes(texto, imagen),
            "secuencia_geometrica": self._es_secuencia_geometrica(texto, imagen),
            "funcion_matematica": self._es_funcion_matematica(texto, imagen),
            "geometria_analitica": self._es_geometria_analitica(texto, imagen),
            "estadistica": self._es_estadistica(texto, imagen)
        }
        
        # Determinar contexto principal
        contexto_principal = max(contextos.items(), key=lambda x: x[1])
        
        return {
            "tipo": contexto_principal[0],
            "confianza": contexto_principal[1],
            "texto_extraido": texto,
            "contextos_detectados": contextos,
            "elementos_visuales": self._analizar_elementos_visuales(imagen)
        }
    
    def _extraer_texto(self, imagen) -> str:
        """Extraer texto de la imagen usando OCR."""
        try:
            # Preprocesar imagen para OCR
            gris = cv2.cvtColor(imagen, cv2.COLOR_BGR2GRAY)
            
            # Mejorar contraste
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            gris = clahe.apply(gris)
            
            # Binarización adaptativa
            binaria = cv2.adaptiveThreshold(
                gris, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # OCR con configuración para español
            config = '--psm 6 -l spa'
            texto = pytesseract.image_to_string(binaria, config=config)
            
            return texto.lower()
            
        except Exception as e:
            print(f"Error en OCR: {e}")
            return ""
    
    def _es_ejercicio_icfes(self, texto: str, imagen) -> float:
        """Detectar si es un ejercicio tipo ICFES."""
        puntuacion = 0.0
        
        # Buscar patrones ICFES
        for patron in self.patrones_icfes:
            if re.search(patron, texto, re.IGNORECASE):
                puntuacion += 0.2
        
        # Buscar opciones múltiples A, B, C, D
        opciones = re.findall(r'[ABCD]\.\s*\d+', texto)
        if len(opciones) >= 3:
            puntuacion += 0.3
        
        # Buscar numeración de ejercicio
        if re.search(r'^\d+\.', texto.strip()):
            puntuacion += 0.1
        
        # Análisis visual de opciones
        if self._detectar_opciones_visuales(imagen):
            puntuacion += 0.2
        
        return min(puntuacion, 1.0)
    
    def _es_secuencia_geometrica(self, texto: str, imagen) -> float:
        """Detectar si es una secuencia geométrica."""
        puntuacion = 0.0
        
        # Buscar patrones de secuencias
        for patron in self.patrones_secuencias:
            if re.search(patron, texto, re.IGNORECASE):
                puntuacion += 0.15
        
        # Buscar patrones geométricos
        for patron in self.patrones_geometria:
            if re.search(patron, texto, re.IGNORECASE):
                puntuacion += 0.15
        
        # Buscar posiciones numeradas
        posiciones = re.findall(r'posición\s+\d+', texto, re.IGNORECASE)
        if len(posiciones) >= 2:
            puntuacion += 0.3
        
        # Análisis visual de figuras repetitivas
        if self._detectar_figuras_repetitivas(imagen):
            puntuacion += 0.4
        
        return min(puntuacion, 1.0)
    
    def _es_funcion_matematica(self, texto: str, imagen) -> float:
        """Detectar si es una función matemática."""
        puntuacion = 0.0
        
        # Buscar patrones de funciones
        patrones_funcion = [r'f\(x\)', r'y\s*=', r'función', r'gráfica']
        for patron in patrones_funcion:
            if re.search(patron, texto, re.IGNORECASE):
                puntuacion += 0.2
        
        # Análisis visual de ejes coordenados
        if self._detectar_ejes_coordenados(imagen):
            puntuacion += 0.4
        
        return min(puntuacion, 1.0)
    
    def _es_geometria_analitica(self, texto: str, imagen) -> float:
        """Detectar si es geometría analítica."""
        puntuacion = 0.0
        
        patrones_geo = [r'coordenadas', r'vector', r'distancia', r'punto']
        for patron in patrones_geo:
            if re.search(patron, texto, re.IGNORECASE):
                puntuacion += 0.2
        
        return min(puntuacion, 1.0)
    
    def _es_estadistica(self, texto: str, imagen) -> float:
        """Detectar si es estadística."""
        puntuacion = 0.0
        
        patrones_est = [r'histograma', r'frecuencia', r'media', r'datos']
        for patron in patrones_est:
            if re.search(patron, texto, re.IGNORECASE):
                puntuacion += 0.2
        
        return min(puntuacion, 1.0)
    
    def _detectar_opciones_visuales(self, imagen) -> bool:
        """Detectar opciones múltiples visualmente."""
        try:
            gris = cv2.cvtColor(imagen, cv2.COLOR_BGR2GRAY)
            
            # Buscar patrones de texto A. B. C. D.
            template_a = cv2.imread('templates/letra_a.png', 0) if Path('templates/letra_a.png').exists() else None
            
            # Por ahora, usar heurística simple
            # En una implementación completa, usaríamos template matching
            return True  # Placeholder
            
        except Exception:
            return False
    
    def _detectar_figuras_repetitivas(self, imagen) -> bool:
        """Detectar figuras geométricas repetitivas."""
        try:
            gris = cv2.cvtColor(imagen, cv2.COLOR_BGR2GRAY)
            
            # Detectar contornos
            contornos, _ = cv2.findContours(
                gris, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )
            
            # Buscar patrones repetitivos
            if len(contornos) >= 3:
                # Analizar si hay figuras similares
                areas = [cv2.contourArea(c) for c in contornos]
                areas_similares = [a for a in areas if 100 < a < 10000]
                
                if len(areas_similares) >= 3:
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _detectar_ejes_coordenados(self, imagen) -> bool:
        """Detectar ejes coordenados en la imagen."""
        try:
            gris = cv2.cvtColor(imagen, cv2.COLOR_BGR2GRAY)
            
            # Detectar líneas con HoughLines
            edges = cv2.Canny(gris, 50, 150, apertureSize=3)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            if lines is not None and len(lines) >= 2:
                # Buscar líneas perpendiculares (ejes)
                angulos = []
                for line in lines:
                    rho, theta = line[0]
                    angulos.append(theta)
                
                # Verificar si hay líneas aproximadamente perpendiculares
                for i, ang1 in enumerate(angulos):
                    for ang2 in angulos[i+1:]:
                        diff = abs(ang1 - ang2)
                        if abs(diff - np.pi/2) < 0.2:  # Aproximadamente 90 grados
                            return True
            
            return False
            
        except Exception:
            return False
    
    def _analizar_elementos_visuales(self, imagen) -> Dict:
        """Analizar elementos visuales de la imagen."""
        try:
            gris = cv2.cvtColor(imagen, cv2.COLOR_BGR2GRAY)
            
            # Detectar contornos
            contornos, _ = cv2.findContours(
                gris, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )
            
            # Detectar líneas
            edges = cv2.Canny(gris, 50, 150)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=50)
            
            # Detectar círculos
            circles = cv2.HoughCircles(
                gris, cv2.HOUGH_GRADIENT, 1, 20,
                param1=50, param2=30, minRadius=5, maxRadius=100
            )
            
            return {
                "num_contornos": len(contornos),
                "num_lineas": len(lines) if lines is not None else 0,
                "num_circulos": len(circles[0]) if circles is not None else 0,
                "tiene_texto": self._tiene_texto_significativo(imagen),
                "tiene_tabla": self._detectar_tabla(imagen)
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def _tiene_texto_significativo(self, imagen) -> bool:
        """Detectar si la imagen tiene texto significativo."""
        try:
            texto = self._extraer_texto(imagen)
            return len(texto.strip()) > 20
        except:
            return False
    
    def _detectar_tabla(self, imagen) -> bool:
        """Detectar si hay una tabla en la imagen."""
        try:
            gris = cv2.cvtColor(imagen, cv2.COLOR_BGR2GRAY)
            
            # Detectar líneas horizontales y verticales
            kernel_h = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
            kernel_v = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))
            
            lineas_h = cv2.morphologyEx(gris, cv2.MORPH_OPEN, kernel_h)
            lineas_v = cv2.morphologyEx(gris, cv2.MORPH_OPEN, kernel_v)
            
            # Si hay suficientes líneas horizontales y verticales, probablemente es una tabla
            return (np.sum(lineas_h > 0) > 1000 and np.sum(lineas_v > 0) > 1000)
            
        except Exception:
            return False

# Función de utilidad para uso externo
def detectar_contexto_imagen(imagen_path: str) -> Dict:
    """Función de conveniencia para detectar contexto de una imagen."""
    detector = DetectorContextoEducativo()
    return detector.detectar_contexto(imagen_path)

if __name__ == "__main__":
    # Test básico
    import sys
    if len(sys.argv) > 1:
        resultado = detectar_contexto_imagen(sys.argv[1])
        print(f"Contexto detectado: {resultado}")
