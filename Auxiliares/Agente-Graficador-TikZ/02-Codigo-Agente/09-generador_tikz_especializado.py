#!/usr/bin/env python3
"""
Generador de Código TikZ Especializado
=====================================
Genera código TikZ específico según el contexto educativo detectado.
"""

import re
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import json

class GeneradorTikzEspecializado:
    """Generador de código TikZ especializado por contexto."""
    
    def __init__(self, templates_dir: str = "05-Templates-TikZ"):
        self.templates_dir = Path(templates_dir)
        self.templates = {
            "ejercicio_icfes": "09-template_icfes_secuencias.tikz",
            "secuencia_triangular": "09-template_icfes_secuencias.tikz",
            "secuencia_cuadrada": "10-template_icfes_cuadrados.tikz",
            "funcion_matematica": "01-template_funciones.tikz",
            "geometria_analitica": "02-template_geometria.tikz"
        }
    
    def generar_codigo(self, contexto: str, analisis_imagen: Dict, 
                      analisis_secuencia: Dict = None) -> Dict:
        """Generar código TikZ especializado según contexto."""
        
        if contexto == "ejercicio_icfes" and analisis_secuencia:
            return self._generar_ejercicio_icfes(analisis_imagen, analisis_secuencia)
        elif contexto == "secuencia_geometrica":
            return self._generar_secuencia_geometrica(analisis_imagen, analisis_secuencia)
        elif contexto == "funcion_matematica":
            return self._generar_funcion_matematica(analisis_imagen)
        else:
            return self._generar_generico(analisis_imagen)
    
    def _generar_ejercicio_icfes(self, analisis_imagen: Dict, 
                                analisis_secuencia: Dict) -> Dict:
        """Generar código TikZ para ejercicio ICFES."""
        
        # Extraer datos de la secuencia
        tipo_secuencia = analisis_secuencia.get("tipo_secuencia", "triangular")
        figuras = analisis_secuencia.get("figuras", [])
        patron = analisis_secuencia.get("patron", {})
        datos_tabla = analisis_secuencia.get("datos_tabla", {})
        
        # Cargar template base
        template_path = self.templates_dir / self.templates["ejercicio_icfes"]
        if not template_path.exists():
            return {"error": f"Template no encontrado: {template_path}"}
        
        with open(template_path, 'r', encoding='utf-8') as f:
            template = f.read()
        
        # Personalizar template con datos específicos
        codigo_personalizado = self._personalizar_template_icfes(
            template, tipo_secuencia, figuras, patron, datos_tabla
        )
        
        # Generar versiones QTikz y LaTeX
        codigo_qtikz = self._extraer_tikzpicture(codigo_personalizado)
        codigo_latex = self._generar_documento_latex(codigo_personalizado)
        
        return {
            "codigo_qtikz": codigo_qtikz,
            "codigo_latex": codigo_latex,
            "tipo_generado": "ejercicio_icfes_especializado",
            "datos_utilizados": {
                "tipo_secuencia": tipo_secuencia,
                "num_figuras": len(figuras),
                "patron_detectado": patron.get("tipo_patron", ""),
                "datos_tabla": datos_tabla
            }
        }
    
    def _personalizar_template_icfes(self, template: str, tipo_secuencia: str,
                                   figuras: List, patron: Dict, datos_tabla: Dict) -> str:
        """Personalizar template ICFES con datos específicos."""
        
        codigo = template
        
        # Personalizar tipo de secuencia
        codigo = codigo.replace(r'\def\tipoSecuencia{triangular}', 
                               f'\\def\\tipoSecuencia{{{tipo_secuencia}}}')
        
        # Personalizar datos de la secuencia
        if patron.get("secuencia"):
            secuencia_str = ",".join(map(str, patron["secuencia"]))
            codigo = codigo.replace(r'\def\datosSecuencia{{1,3,6,10}}',
                                   f'\\def\\datosSecuencia{{{{{secuencia_str}}}}}')
        
        # Personalizar datos de tabla si están disponibles
        if datos_tabla.get("areas"):
            areas = datos_tabla["areas"]
            # Reemplazar valores en la tabla
            for i, area in enumerate(areas[:4], 1):
                codigo = re.sub(f'(Área.*?{i}.*?)\\d+', f'\\1{area}', codigo)
        
        # Personalizar opciones de respuesta si están en el texto
        opciones = self._extraer_opciones_respuesta(datos_tabla)
        if opciones:
            codigo = codigo.replace(r'\def\opcionA{45}', f'\\def\\opcionA{{{opciones.get("A", 45)}}}')
            codigo = codigo.replace(r'\def\opcionB{55}', f'\\def\\opcionB{{{opciones.get("B", 55)}}}')
            codigo = codigo.replace(r'\def\opcionC{56}', f'\\def\\opcionC{{{opciones.get("C", 56)}}}')
            codigo = codigo.replace(r'\def\opcionD{66}', f'\\def\\opcionD{{{opciones.get("D", 66)}}}')
        
        # Personalizar número de ejercicio si está disponible
        numero_ejercicio = self._extraer_numero_ejercicio(datos_tabla)
        if numero_ejercicio:
            codigo = codigo.replace(r'\def\numeroEjercicio{7}',
                                   f'\\def\\numeroEjercicio{{{numero_ejercicio}}}')
        
        # Personalizar posición de la pregunta
        posicion_pregunta = self._extraer_posicion_pregunta(datos_tabla)
        if posicion_pregunta:
            codigo = codigo.replace(r'\def\posicionPregunta{9}',
                                   f'\\def\\posicionPregunta{{{posicion_pregunta}}}')
        
        return codigo
    
    def _generar_secuencia_geometrica(self, analisis_imagen: Dict,
                                    analisis_secuencia: Dict) -> Dict:
        """Generar código TikZ para secuencia geométrica general."""
        
        tipo_secuencia = analisis_secuencia.get("tipo_secuencia", "triangular")
        figuras = analisis_secuencia.get("figuras", [])
        
        # Generar código específico según tipo
        if tipo_secuencia == "triangular":
            codigo = self._generar_secuencia_triangular(figuras)
        elif tipo_secuencia == "cuadrada":
            codigo = self._generar_secuencia_cuadrada(figuras)
        else:
            codigo = self._generar_secuencia_generica(figuras)
        
        return {
            "codigo_qtikz": self._extraer_tikzpicture(codigo),
            "codigo_latex": self._generar_documento_latex(codigo),
            "tipo_generado": f"secuencia_{tipo_secuencia}"
        }
    
    def _generar_secuencia_triangular(self, figuras: List) -> str:
        """Generar código específico para números triangulares."""
        
        codigo = """
% Secuencia de números triangulares generada automáticamente
\\begin{tikzpicture}[scale=1.2]

% Configuración de colores
\\definecolor{puntoColor}{RGB}{139,69,19}
\\definecolor{lineaColor}{RGB}{0,0,0}

"""
        
        # Generar cada figura de la secuencia
        for i, figura in enumerate(figuras[:4], 1):
            x_base = i * 3 - 2
            codigo += f"""
% Posición {i}: {figura.puntos} puntos
\\node[below, font=\\bfseries] at ({x_base}, 0.3) {{Posición {i}}};
"""
            
            # Generar puntos según estructura triangular
            codigo += self._generar_triangulo_puntos(x_base, 1, i, figura.puntos)
        
        codigo += "\n\\end{tikzpicture}"
        return codigo
    
    def _generar_triangulo_puntos(self, x_center: float, y_base: float, 
                                 nivel: int, num_puntos: int) -> str:
        """Generar puntos en estructura triangular."""
        
        codigo = ""
        puntos_colocados = 0
        
        # Calcular número de filas necesarias
        filas = 1
        while filas * (filas + 1) // 2 < num_puntos:
            filas += 1
        
        # Generar puntos fila por fila (de abajo hacia arriba)
        for fila in range(filas):
            puntos_en_fila = min(filas - fila, num_puntos - puntos_colocados)
            if puntos_en_fila <= 0:
                break
            
            y = y_base + fila * 0.5
            x_start = x_center - (puntos_en_fila - 1) * 0.25
            
            for punto in range(puntos_en_fila):
                x = x_start + punto * 0.5
                codigo += f"\\fill[puntoColor] ({x}, {y}) circle (3pt);\n"
                puntos_colocados += 1
        
        # Agregar líneas del triángulo si hay más de un punto
        if num_puntos > 1:
            # Calcular vértices del triángulo exterior
            altura = (filas - 1) * 0.5
            ancho = (filas - 1) * 0.5
            
            codigo += f"""\\draw[lineaColor, thick] ({x_center - ancho}, {y_base}) -- 
                         ({x_center}, {y_base + altura}) -- 
                         ({x_center + ancho}, {y_base}) -- cycle;\n"""
        
        return codigo
    
    def _generar_funcion_matematica(self, analisis_imagen: Dict) -> Dict:
        """Generar código TikZ para función matemática."""
        
        # Usar template de funciones existente
        template_path = self.templates_dir / self.templates["funcion_matematica"]
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                codigo = f.read()
        else:
            codigo = self._generar_funcion_basica()
        
        return {
            "codigo_qtikz": self._extraer_tikzpicture(codigo),
            "codigo_latex": self._generar_documento_latex(codigo),
            "tipo_generado": "funcion_matematica"
        }
    
    def _extraer_tikzpicture(self, codigo_completo: str) -> str:
        """Extraer solo el contenido tikzpicture para QTikz."""
        
        # Buscar inicio y fin de tikzpicture
        inicio = codigo_completo.find('\\begin{tikzpicture}')
        fin = codigo_completo.find('\\end{tikzpicture}') + len('\\end{tikzpicture}')
        
        if inicio != -1 and fin != -1:
            tikz_content = codigo_completo[inicio:fin]
            
            # Agregar encabezado para QTikz
            header = """% ==============================
% CÓDIGO TIKZ PARA QTIKZ/KTIKZ
% ==============================
% Generado por Agente TikZ Especializado
% Compatible con QTikz/KTikz (solo código TikZ puro)

"""
            return header + tikz_content
        else:
            return codigo_completo
    
    def _generar_documento_latex(self, codigo_tikz: str) -> str:
        """Generar documento LaTeX completo."""
        
        if '\\documentclass' in codigo_tikz:
            return codigo_tikz
        
        # Extraer solo tikzpicture si existe
        tikz_content = codigo_tikz
        if '\\begin{tikzpicture}' in codigo_tikz:
            inicio = codigo_tikz.find('\\begin{tikzpicture}')
            fin = codigo_tikz.find('\\end{tikzpicture}') + len('\\end{tikzpicture}')
            tikz_content = codigo_tikz[inicio:fin]
        
        documento = f"""% ==============================
% DOCUMENTO LATEX COMPLETO CON TIKZ
% ==============================
% Generado por Agente TikZ Especializado
% Para compilación independiente con pdflatex

\\documentclass{{standalone}}
\\usepackage{{tikz}}
\\usepackage{{pgfplots}}
\\usetikzlibrary{{calc,arrows.meta,positioning,patterns}}
\\pgfplotsset{{compat=1.18}}

\\begin{{document}}

{tikz_content}

\\end{{document}}"""
        
        return documento
    
    def _extraer_opciones_respuesta(self, datos_tabla: Dict) -> Dict:
        """Extraer opciones de respuesta del texto."""
        opciones = {}
        
        # Buscar en datos de tabla o texto extraído
        texto = str(datos_tabla)
        
        # Patrones para opciones A, B, C, D
        patrones = {
            'A': r'A\.\s*(\d+)',
            'B': r'B\.\s*(\d+)',
            'C': r'C\.\s*(\d+)',
            'D': r'D\.\s*(\d+)'
        }
        
        for letra, patron in patrones.items():
            match = re.search(patron, texto)
            if match:
                opciones[letra] = int(match.group(1))
        
        return opciones
    
    def _extraer_numero_ejercicio(self, datos_tabla: Dict) -> Optional[int]:
        """Extraer número del ejercicio."""
        texto = str(datos_tabla)
        match = re.search(r'^(\d+)\.', texto.strip())
        return int(match.group(1)) if match else None
    
    def _extraer_posicion_pregunta(self, datos_tabla: Dict) -> Optional[int]:
        """Extraer posición preguntada en el ejercicio."""
        texto = str(datos_tabla)
        match = re.search(r'figura\s+(\d+)\s+es:', texto)
        return int(match.group(1)) if match else None
    
    def _generar_generico(self, analisis_imagen: Dict) -> Dict:
        """Generar código TikZ genérico."""
        
        codigo = """% Código TikZ genérico generado automáticamente
\\begin{tikzpicture}[scale=1.2]

% Cuadrícula de fondo sutil
\\draw[gray!20, thin] (-4,-3) grid[step=0.5] (4,3);

% Ejes coordenados principales
\\draw[thick, ->] (-4,0) -- (4,0) node[below right] {$x$};
\\draw[thick, ->] (0,-3) -- (0,3) node[above left] {$y$};

% Elemento principal (personalizar según análisis)
\\draw[blue, very thick] plot[domain=-3:3, samples=50] (\\x, {0.5*\\x^2 - 1});

% Puntos importantes
\\fill[red] (0,-1) circle (3pt) node[below right] {Punto importante};

% Marcas en ejes
\\foreach \\x in {-3,-2,-1,1,2,3}
  \\draw (\\x,-0.1) -- (\\x,0.1) node[below] {\\x};
\\foreach \\y in {-2,-1,1,2}
  \\draw (-0.1,\\y) -- (0.1,\\y) node[left] {\\y};

\\end{tikzpicture}"""
        
        return {
            "codigo_qtikz": self._extraer_tikzpicture(codigo),
            "codigo_latex": self._generar_documento_latex(codigo),
            "tipo_generado": "generico"
        }

# Función de utilidad
def generar_codigo_especializado(contexto: str, analisis_imagen: Dict,
                                analisis_secuencia: Dict = None) -> Dict:
    """Función de conveniencia para generar código especializado."""
    generador = GeneradorTikzEspecializado()
    return generador.generar_codigo(contexto, analisis_imagen, analisis_secuencia)

if __name__ == "__main__":
    # Test básico
    generador = GeneradorTikzEspecializado()
    resultado = generador._generar_generico({})
    print(resultado["codigo_qtikz"])
