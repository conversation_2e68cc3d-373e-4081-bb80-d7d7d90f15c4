#!/usr/bin/env python3
"""
Sistema de Prompts Especializados para Agente TikZ
=================================================
Genera prompts específicos según el contexto educativo detectado.
"""

from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class PromptEspecializado:
    """Representa un prompt especializado para un contexto específico."""
    contexto: str
    prompt_base: str
    instrucciones_especificas: List[str]
    elementos_clave: List[str]
    estilo_tikz: str

class GeneradorPrompts:
    """Generador de prompts especializados según contexto."""
    
    def __init__(self):
        self.prompts_base = {
            "ejercicio_icfes": self._crear_prompt_icfes(),
            "secuencia_geometrica": self._crear_prompt_secuencias(),
            "funcion_matematica": self._crear_prompt_funciones(),
            "geometria_analitica": self._crear_prompt_geometria(),
            "estadistica": self._crear_prompt_estadistica(),
            "calculo": self._crear_prompt_calculo(),
            "algebra": self._crear_prompt_algebra()
        }
    
    def generar_prompt(self, contexto: str, datos_contexto: Dict = None, 
                      prompt_usuario: str = "") -> str:
        """Generar prompt especializado según contexto."""
        
        # Obtener prompt base
        prompt_especializado = self.prompts_base.get(
            contexto, self.prompts_base["funcion_matematica"]
        )
        
        # Personalizar según datos específicos
        prompt_final = self._personalizar_prompt(
            prompt_especializado, datos_contexto, prompt_usuario
        )
        
        return prompt_final
    
    def _crear_prompt_icfes(self) -> PromptEspecializado:
        """Crear prompt especializado para ejercicios ICFES."""
        return PromptEspecializado(
            contexto="ejercicio_icfes",
            prompt_base="""
            🎓 ANÁLISIS DE EJERCICIO ICFES - MATEMÁTICAS
            
            Esta imagen contiene un ejercicio tipo ICFES de matemáticas. Analiza con enfoque educativo:
            
            1. **IDENTIFICACIÓN DEL EJERCICIO**:
               - Número del ejercicio y enunciado completo
               - Tipo de problema matemático (secuencias, geometría, funciones, etc.)
               - Nivel de dificultad (básico, intermedio, avanzado)
            
            2. **ELEMENTOS EDUCATIVOS**:
               - Conceptos matemáticos involucrados
               - Habilidades que evalúa (interpretación, cálculo, razonamiento)
               - Estrategias de solución recomendadas
            
            3. **ANÁLISIS VISUAL ESPECÍFICO**:
               - Figuras geométricas y su progresión
               - Tablas de datos y patrones numéricos
               - Gráficas y sistemas coordenados
               - Elementos destacados para el estudiante
            
            4. **OPCIONES DE RESPUESTA**:
               - Identificar opciones A, B, C, D
               - Analizar distractores comunes
               - Respuesta correcta y justificación
            
            Genera código TikZ EDUCATIVO que:
            - Sea claro y pedagógico para estudiantes de bachillerato
            - Use colores contrastados y elementos destacados
            - Incluya etiquetas y anotaciones explicativas
            - Mantenga proporciones apropiadas para proyección
            - Facilite la comprensión del concepto matemático
            """,
            instrucciones_especificas=[
                "Usar escala grande para proyección (scale=1.5 o mayor)",
                "Colores educativos: azul, rojo, verde, negro",
                "Grosor de líneas thick o very thick",
                "Etiquetas grandes y claras",
                "Incluir título del ejercicio",
                "Mostrar opciones de respuesta",
                "Destacar elementos clave con colores"
            ],
            elementos_clave=[
                "numero_ejercicio", "enunciado", "figuras_secuencia", 
                "tabla_datos", "opciones_respuesta", "elementos_destacados"
            ],
            estilo_tikz="educativo_icfes"
        )
    
    def _crear_prompt_secuencias(self) -> PromptEspecializado:
        """Crear prompt para secuencias geométricas."""
        return PromptEspecializado(
            contexto="secuencia_geometrica",
            prompt_base="""
            📐 ANÁLISIS DE SECUENCIA GEOMÉTRICA
            
            Esta imagen muestra una secuencia de figuras geométricas. Analiza específicamente:
            
            1. **TIPO DE SECUENCIA**:
               - Números triangulares, cuadrados, pentagonales, etc.
               - Patrón de crecimiento (aritmético, cuadrático, etc.)
               - Fórmula matemática subyacente
            
            2. **FIGURAS INDIVIDUALES**:
               - Posición en la secuencia (1, 2, 3, 4, ...)
               - Número de puntos o elementos en cada figura
               - Estructura geométrica (triángulos, cuadrados, etc.)
               - Organización interna de los elementos
            
            3. **PATRÓN MATEMÁTICO**:
               - Diferencias entre términos consecutivos
               - Relación con fórmulas conocidas
               - Predicción de términos futuros
               - Conexión con conceptos algebraicos
            
            4. **REPRESENTACIÓN VISUAL**:
               - Disposición espacial de las figuras
               - Progresión visual del crecimiento
               - Elementos de conexión entre figuras
               - Indicadores de continuidad
            
            Genera código TikZ que muestre:
            - Cada figura de la secuencia claramente diferenciada
            - Puntos individuales bien marcados
            - Estructura geométrica de cada figura
            - Etiquetas de posición y cantidad
            - Indicación de continuidad de la secuencia
            """,
            instrucciones_especificas=[
                "Dibujar cada figura por separado",
                "Marcar puntos individuales con \\fill[color] circle",
                "Usar líneas para mostrar estructura geométrica",
                "Etiquetar posiciones (Posición 1, 2, 3, ...)",
                "Incluir número de puntos en cada figura",
                "Mostrar progresión visual clara",
                "Usar colores consistentes"
            ],
            elementos_clave=[
                "figuras_individuales", "puntos_marcados", "estructura_geometrica",
                "etiquetas_posicion", "patron_crecimiento", "continuidad"
            ],
            estilo_tikz="secuencias_geometricas"
        )
    
    def _crear_prompt_funciones(self) -> PromptEspecializado:
        """Crear prompt para funciones matemáticas."""
        return PromptEspecializado(
            contexto="funcion_matematica",
            prompt_base="""
            📈 ANÁLISIS DE FUNCIÓN MATEMÁTICA
            
            Esta imagen contiene una función matemática. Analiza con precisión:
            
            1. **TIPO DE FUNCIÓN**:
               - Lineal, cuadrática, cúbica, trigonométrica, exponencial, logarítmica
               - Grado y características principales
               - Dominio y rango visibles
            
            2. **CARACTERÍSTICAS ANALÍTICAS**:
               - Puntos de intersección con ejes
               - Máximos y mínimos locales y globales
               - Puntos de inflexión
               - Asíntotas (horizontales, verticales, oblicuas)
               - Comportamiento en los extremos
            
            3. **ELEMENTOS GRÁFICOS**:
               - Sistema de coordenadas y escalas
               - Cuadrícula de referencia
               - Marcas en los ejes
               - Puntos especiales destacados
               - Etiquetas y anotaciones
            
            4. **PROPIEDADES MATEMÁTICAS**:
               - Continuidad y diferenciabilidad
               - Crecimiento y decrecimiento
               - Concavidad y convexidad
               - Simetrías (par, impar, periódica)
            
            Genera código TikZ matemáticamente preciso con:
            - Función principal con alta resolución (samples=100+)
            - Ejes coordenados con marcas apropiadas
            - Puntos críticos claramente marcados
            - Etiquetas matemáticas correctas
            - Escala apropiada para visualización
            """,
            instrucciones_especificas=[
                "Usar plot[domain=..., samples=100] para curvas suaves",
                "Marcar puntos críticos con \\fill circle",
                "Incluir cuadrícula de fondo sutil",
                "Etiquetar ejes con variables apropiadas",
                "Usar notación matemática LaTeX",
                "Destacar características importantes",
                "Mantener proporciones matemáticamente correctas"
            ],
            elementos_clave=[
                "curva_principal", "ejes_coordenados", "puntos_criticos",
                "intersecciones", "asintotas", "etiquetas_matematicas"
            ],
            estilo_tikz="funciones_matematicas"
        )
    
    def _crear_prompt_geometria(self) -> PromptEspecializado:
        """Crear prompt para geometría analítica."""
        return PromptEspecializado(
            contexto="geometria_analitica",
            prompt_base="""
            📐 ANÁLISIS DE GEOMETRÍA ANALÍTICA
            
            Esta imagen contiene elementos de geometría analítica. Analiza:
            
            1. **FIGURAS GEOMÉTRICAS**:
               - Puntos, líneas, segmentos, rayos
               - Triángulos, cuadriláteros, polígonos
               - Círculos, elipses, parábolas, hipérbolas
               - Vectores y sus componentes
            
            2. **COORDENADAS Y MEDIDAS**:
               - Coordenadas de puntos importantes
               - Distancias entre puntos
               - Ángulos y sus medidas
               - Áreas y perímetros
            
            3. **RELACIONES GEOMÉTRICAS**:
               - Paralelismo y perpendicularidad
               - Congruencia y semejanza
               - Transformaciones geométricas
               - Propiedades métricas
            
            4. **CONSTRUCCIONES**:
               - Mediatrices y bisectrices
               - Alturas y medianas
               - Círculos notables
               - Lugares geométricos
            
            Genera código TikZ geométricamente preciso.
            """,
            instrucciones_especificas=[
                "Usar coordenadas exactas",
                "Marcar puntos importantes",
                "Incluir medidas y ángulos",
                "Usar construcciones geométricas precisas",
                "Etiquetar elementos claramente"
            ],
            elementos_clave=[
                "puntos_coordenados", "figuras_geometricas", "medidas",
                "angulos", "construcciones", "relaciones"
            ],
            estilo_tikz="geometria_analitica"
        )
    
    def _crear_prompt_estadistica(self) -> PromptEspecializado:
        """Crear prompt para estadística."""
        return PromptEspecializado(
            contexto="estadistica",
            prompt_base="""
            📊 ANÁLISIS ESTADÍSTICO
            
            Esta imagen contiene elementos estadísticos. Analiza:
            
            1. **TIPO DE GRÁFICA**:
               - Histograma, diagrama de barras, circular
               - Diagrama de dispersión, boxplot
               - Curva de distribución
            
            2. **DATOS Y VARIABLES**:
               - Variables cuantitativas y cualitativas
               - Frecuencias y distribuciones
               - Medidas de tendencia central
               - Medidas de dispersión
            
            3. **INTERPRETACIÓN**:
               - Patrones y tendencias
               - Valores atípicos
               - Correlaciones
               - Conclusiones estadísticas
            
            Genera código TikZ estadísticamente apropiado.
            """,
            instrucciones_especificas=[
                "Usar barras proporcionales",
                "Incluir escalas apropiadas",
                "Etiquetar datos claramente",
                "Usar colores diferenciadores"
            ],
            elementos_clave=[
                "datos_estadisticos", "graficas", "distribuciones",
                "medidas_centrales", "interpretacion"
            ],
            estilo_tikz="estadistica"
        )
    
    def _crear_prompt_calculo(self) -> PromptEspecializado:
        """Crear prompt para cálculo."""
        return PromptEspecializado(
            contexto="calculo",
            prompt_base="""
            ∫ ANÁLISIS DE CÁLCULO
            
            Esta imagen contiene elementos de cálculo diferencial o integral.
            """,
            instrucciones_especificas=[
                "Mostrar derivadas y tangentes",
                "Sombrear áreas de integración",
                "Marcar puntos críticos"
            ],
            elementos_clave=[
                "derivadas", "integrales", "limites", "tangentes"
            ],
            estilo_tikz="calculo"
        )
    
    def _crear_prompt_algebra(self) -> PromptEspecializado:
        """Crear prompt para álgebra."""
        return PromptEspecializado(
            contexto="algebra",
            prompt_base="""
            🔢 ANÁLISIS ALGEBRAICO
            
            Esta imagen contiene elementos algebraicos.
            """,
            instrucciones_especificas=[
                "Mostrar sistemas de ecuaciones",
                "Representar soluciones gráficamente",
                "Incluir intersecciones"
            ],
            elementos_clave=[
                "ecuaciones", "sistemas", "soluciones", "intersecciones"
            ],
            estilo_tikz="algebra"
        )
    
    def _personalizar_prompt(self, prompt_base: PromptEspecializado, 
                           datos_contexto: Dict = None, 
                           prompt_usuario: str = "") -> str:
        """Personalizar prompt según datos específicos."""
        
        prompt_final = prompt_base.prompt_base
        
        # Agregar información específica del contexto
        if datos_contexto:
            if "tipo_secuencia" in datos_contexto:
                tipo = datos_contexto["tipo_secuencia"]
                prompt_final += f"\n\n🎯 TIPO ESPECÍFICO DETECTADO: {tipo.upper()}\n"
                
                if tipo == "triangular":
                    prompt_final += """
                    Esta es una secuencia de NÚMEROS TRIANGULARES.
                    - Fórmula: T(n) = n(n+1)/2
                    - Secuencia: 1, 3, 6, 10, 15, 21, ...
                    - Cada figura forma un triángulo con puntos
                    - Genera código TikZ que muestre claramente la estructura triangular
                    """
                elif tipo == "cuadrada":
                    prompt_final += """
                    Esta es una secuencia de NÚMEROS CUADRADOS.
                    - Fórmula: C(n) = n²
                    - Secuencia: 1, 4, 9, 16, 25, ...
                    - Cada figura forma un cuadrado con puntos
                    """
            
            if "datos_tabla" in datos_contexto:
                datos = datos_contexto["datos_tabla"]
                prompt_final += f"\n\n📊 DATOS DE TABLA DETECTADOS: {datos}\n"
                prompt_final += "Incluye esta tabla en el código TikZ generado.\n"
        
        # Agregar prompt específico del usuario
        if prompt_usuario:
            prompt_final += f"\n\n👤 INSTRUCCIONES ESPECÍFICAS DEL USUARIO:\n{prompt_usuario}\n"
        
        # Agregar instrucciones finales
        prompt_final += f"""
        
        🎨 INSTRUCCIONES TIKZ ESPECÍFICAS:
        {chr(10).join('- ' + inst for inst in prompt_base.instrucciones_especificas)}
        
        📋 ELEMENTOS CLAVE A INCLUIR:
        {', '.join(prompt_base.elementos_clave)}
        
        🎯 ESTILO REQUERIDO: {prompt_base.estilo_tikz}
        
        Genera código TikZ profesional, educativo y matemáticamente preciso.
        """
        
        return prompt_final

# Función de utilidad
def generar_prompt_especializado(contexto: str, datos_contexto: Dict = None, 
                                prompt_usuario: str = "") -> str:
    """Función de conveniencia para generar prompt especializado."""
    generador = GeneradorPrompts()
    return generador.generar_prompt(contexto, datos_contexto, prompt_usuario)

if __name__ == "__main__":
    # Test básico
    generador = GeneradorPrompts()
    prompt = generador.generar_prompt("ejercicio_icfes", 
                                     {"tipo_secuencia": "triangular"})
    print(prompt)
