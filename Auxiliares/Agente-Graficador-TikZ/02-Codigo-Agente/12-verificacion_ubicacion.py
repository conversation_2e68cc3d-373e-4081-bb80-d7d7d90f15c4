#!/usr/bin/env python3
"""
🔍 Verificación de Ubicación y Funcionamiento
============================================
Verifica que todo funcione correctamente desde la nueva ubicación.
"""

import os
import sys
from pathlib import Path

def verificar_estructura():
    """Verificar estructura de directorios."""
    print("🔍 VERIFICACIÓN DE ESTRUCTURA")
    print("-" * 40)
    
    directorio_actual = Path.cwd()
    print(f"📁 Directorio actual: {directorio_actual}")
    
    # Verificar que estamos en la ubicación correcta
    if "Agente-Graficador-TikZ/02-Codigo-Agente" in str(directorio_actual):
        print("✅ Ubicación correcta")
    else:
        print("❌ Ubicación incorrecta")
        return False
    
    # Verificar archivos principales
    archivos_principales = [
        "01-agente_principal.py",
        "01-agente_principal_mejorado.py",
        "06-detector_contexto.py",
        "07-analizador_secuencias.py",
        "08-prompts_especializados.py",
        "09-generador_tikz_especializado.py",
        "10-test_mejoras.py"
    ]
    
    for archivo in archivos_principales:
        if Path(archivo).exists():
            print(f"✅ {archivo}")
        else:
            print(f"❌ {archivo} - FALTANTE")
    
    # Verificar directorios
    directorios = [
        "tikz_generado",
        "../04-Ejemplos-y-Pruebas",
        "../05-Templates-TikZ",
        "../03-Configuracion-VSCode"
    ]
    
    for directorio in directorios:
        if Path(directorio).exists():
            print(f"✅ {directorio}/")
        else:
            print(f"❌ {directorio}/ - FALTANTE")
    
    print()
    return True

def verificar_imagen_ejemplo():
    """Verificar imagen de ejemplo."""
    print("🖼️ VERIFICACIÓN DE IMAGEN DE EJEMPLO")
    print("-" * 40)
    
    imagen_ejemplo = Path("../04-Ejemplos-y-Pruebas/01-imagen_ejemplo.png")
    if imagen_ejemplo.exists():
        print(f"✅ Imagen de ejemplo encontrada: {imagen_ejemplo}")
        print(f"📊 Tamaño: {imagen_ejemplo.stat().st_size} bytes")
        return str(imagen_ejemplo)
    else:
        print("❌ Imagen de ejemplo no encontrada")
        return None

def test_agente_simple():
    """Test simple del agente."""
    print("🧪 TEST SIMPLE DEL AGENTE")
    print("-" * 40)
    
    try:
        # Importar el agente principal
        sys.path.append('.')
        
        # Test básico de importación
        print("📦 Intentando importar módulos...")
        
        # Test del agente clásico
        if Path("01-agente_principal.py").exists():
            print("✅ Agente principal clásico disponible")
        
        # Test del agente mejorado
        if Path("01-agente_principal_mejorado.py").exists():
            print("✅ Agente principal mejorado disponible")
        
        # Test de módulos especializados
        modulos_especializados = [
            "06-detector_contexto.py",
            "07-analizador_secuencias.py",
            "08-prompts_especializados.py",
            "09-generador_tikz_especializado.py"
        ]
        
        for modulo in modulos_especializados:
            if Path(modulo).exists():
                print(f"✅ {modulo} disponible")
            else:
                print(f"⚠️ {modulo} no disponible (modo compatibilidad)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en test: {e}")
        return False

def verificar_tasks_vscode():
    """Verificar configuración de VSCode."""
    print("⚙️ VERIFICACIÓN DE TASKS VSCODE")
    print("-" * 40)
    
    tasks_file = Path("../03-Configuracion-VSCode/01-tasks.json")
    if tasks_file.exists():
        print("✅ Archivo de tasks encontrado")
        
        with open(tasks_file, 'r', encoding='utf-8') as f:
            contenido = f.read()
        
        # Verificar que las rutas estén actualizadas
        if "Auxiliares/Agente-Graficador-TikZ" in contenido:
            print("✅ Rutas actualizadas en tasks.json")
        else:
            print("⚠️ Rutas pueden necesitar actualización")
        
        # Verificar tasks principales
        tasks_esperados = [
            "Agente TikZ v2.0: Analizar Imagen",
            "Agente TikZ v2.0: Análisis Personalizado",
            "Test Mejoras Agente v2.0"
        ]
        
        for task in tasks_esperados:
            if task in contenido:
                print(f"✅ Task: {task}")
            else:
                print(f"❌ Task faltante: {task}")
        
    else:
        print("❌ Archivo de tasks no encontrado")
    
    print()

def crear_imagen_test():
    """Crear imagen de test simple si no existe."""
    print("🎨 CREANDO IMAGEN DE TEST")
    print("-" * 40)
    
    try:
        import matplotlib.pyplot as plt
        import numpy as np
        
        # Crear una función simple
        x = np.linspace(-3, 3, 100)
        y = 0.5 * x**2 - 1
        
        plt.figure(figsize=(8, 6))
        plt.plot(x, y, 'b-', linewidth=2, label='f(x) = 0.5x² - 1')
        plt.grid(True, alpha=0.3)
        plt.axhline(y=0, color='k', linewidth=0.5)
        plt.axvline(x=0, color='k', linewidth=0.5)
        plt.xlabel('x')
        plt.ylabel('y')
        plt.title('Función Cuadrática - Test')
        plt.legend()
        
        # Guardar en directorio de ejemplos
        test_image_path = Path("../04-Ejemplos-y-Pruebas/test_function.png")
        plt.savefig(test_image_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ Imagen de test creada: {test_image_path}")
        return str(test_image_path)
        
    except ImportError:
        print("⚠️ matplotlib no disponible, usando imagen existente")
        return None
    except Exception as e:
        print(f"❌ Error creando imagen: {e}")
        return None

def mostrar_instrucciones_uso():
    """Mostrar instrucciones de uso."""
    print("📋 INSTRUCCIONES DE USO DESDE NUEVA UBICACIÓN")
    print("=" * 60)
    
    print("\n🎯 UBICACIÓN CORRECTA:")
    print("   /Auxiliares/Agente-Graficador-TikZ/02-Codigo-Agente/")
    
    print("\n🚀 COMANDOS DESDE TERMINAL:")
    print("   # Agente v2.0 mejorado:")
    print("   python3 01-agente_principal_mejorado.py ../04-Ejemplos-y-Pruebas/01-imagen_ejemplo.png")
    print()
    print("   # Agente v1.0 clásico:")
    print("   python3 01-agente_principal.py ../04-Ejemplos-y-Pruebas/01-imagen_ejemplo.png")
    print()
    print("   # Test de mejoras:")
    print("   python3 10-test_mejoras.py")
    
    print("\n⚙️ TASKS DE VSCODE:")
    print("   1. Ctrl+Shift+P → 'Tasks: Run Task'")
    print("   2. Seleccionar: '🎨 Agente TikZ v2.0: Analizar Imagen (Mejorado)'")
    print("   3. Seleccionar imagen a analizar")
    
    print("\n📁 RESULTADOS:")
    print("   - tikz_generado/[nombre]_agente.tikz    (LaTeX completo)")
    print("   - tikz_generado/[nombre]_qtikz.tikz     (Solo TikZ para QTikz)")
    print("   - tikz_generado/[nombre]_analisis.json  (Metadatos)")
    
    print("\n🎨 ABRIR EN QTIKZ:")
    print("   qtikz tikz_generado/[archivo]_qtikz.tikz")

def main():
    """Función principal de verificación."""
    print("🔍 VERIFICACIÓN COMPLETA DEL AGENTE TIKZ")
    print("=" * 60)
    print()
    
    # Ejecutar verificaciones
    estructura_ok = verificar_estructura()
    imagen_ejemplo = verificar_imagen_ejemplo()
    agente_ok = test_agente_simple()
    verificar_tasks_vscode()
    
    # Crear imagen de test si es necesario
    if not imagen_ejemplo:
        imagen_test = crear_imagen_test()
    
    # Mostrar resumen
    print("📊 RESUMEN DE VERIFICACIÓN")
    print("-" * 40)
    
    if estructura_ok:
        print("✅ Estructura de directorios: OK")
    else:
        print("❌ Estructura de directorios: PROBLEMAS")
    
    if imagen_ejemplo:
        print("✅ Imagen de ejemplo: Disponible")
    else:
        print("⚠️ Imagen de ejemplo: No encontrada")
    
    if agente_ok:
        print("✅ Agentes: Funcionales")
    else:
        print("❌ Agentes: Problemas detectados")
    
    print()
    
    # Mostrar instrucciones
    mostrar_instrucciones_uso()
    
    print("\n🎉 VERIFICACIÓN COMPLETADA")
    
    if estructura_ok and agente_ok:
        print("✅ Todo listo para usar desde la nueva ubicación!")
    else:
        print("⚠️ Algunos problemas detectados, revisar arriba.")

if __name__ == "__main__":
    main()
