#!/usr/bin/env python3
"""
Analizador de Secuencias Geométricas para Agente TikZ
====================================================
Analiza secuencias de figuras geométricas y genera código TikZ especializado.
"""

import cv2
import numpy as np
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

@dataclass
class FiguraGeometrica:
    """Representa una figura geométrica en la secuencia."""
    posicion: int
    tipo: str  # 'triangular', 'cuadrada', 'circular'
    puntos: int
    coordenadas: List[Tuple[float, float]]
    area: Optional[float] = None
    perimetro: Optional[float] = None

class AnalizadorSecuencias:
    """Analizador especializado para secuencias geométricas."""
    
    def __init__(self):
        self.tipos_secuencia = {
            'triangular': self._analizar_triangular,
            'cuadrada': self._analizar_cuadrada,
            'pentagonal': self._analizar_pentagonal,
            'fibonacci': self._analizar_fibonacci
        }
    
    def analizar_secuencia(self, imagen_path: str, texto: str = "") -> Dict:
        """Analizar secuencia geométrica en la imagen."""
        
        # Cargar imagen
        imagen = cv2.imread(imagen_path)
        if imagen is None:
            return {"error": "No se pudo cargar la imagen"}
        
        # Detectar tipo de secuencia
        tipo_secuencia = self._detectar_tipo_secuencia(texto, imagen)
        
        # Extraer figuras de la secuencia
        figuras = self._extraer_figuras(imagen, tipo_secuencia)
        
        # Analizar patrón
        patron = self._analizar_patron(figuras, tipo_secuencia)
        
        # Extraer datos de tabla si existe
        datos_tabla = self._extraer_datos_tabla(texto)
        
        return {
            "tipo_secuencia": tipo_secuencia,
            "figuras": figuras,
            "patron": patron,
            "datos_tabla": datos_tabla,
            "formula": self._deducir_formula(patron, tipo_secuencia),
            "predicciones": self._generar_predicciones(patron, tipo_secuencia)
        }
    
    def _detectar_tipo_secuencia(self, texto: str, imagen) -> str:
        """Detectar el tipo de secuencia geométrica."""
        
        # Buscar pistas en el texto
        if re.search(r'triangular|triángulo', texto, re.IGNORECASE):
            return 'triangular'
        elif re.search(r'cuadrad|cuadrado', texto, re.IGNORECASE):
            return 'cuadrada'
        elif re.search(r'pentagonal|pentágono', texto, re.IGNORECASE):
            return 'pentagonal'
        elif re.search(r'fibonacci', texto, re.IGNORECASE):
            return 'fibonacci'
        
        # Análisis visual para determinar tipo
        return self._analizar_visual_tipo(imagen)
    
    def _analizar_visual_tipo(self, imagen) -> str:
        """Analizar visualmente el tipo de secuencia."""
        try:
            gris = cv2.cvtColor(imagen, cv2.COLOR_BGR2GRAY)
            
            # Detectar contornos
            contornos, _ = cv2.findContours(
                gris, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )
            
            # Analizar formas de los contornos
            formas_triangulares = 0
            formas_cuadradas = 0
            
            for contorno in contornos:
                # Aproximar contorno
                epsilon = 0.02 * cv2.arcLength(contorno, True)
                approx = cv2.approxPolyDP(contorno, epsilon, True)
                
                if len(approx) == 3:
                    formas_triangulares += 1
                elif len(approx) == 4:
                    formas_cuadradas += 1
            
            if formas_triangulares > formas_cuadradas:
                return 'triangular'
            elif formas_cuadradas > formas_triangulares:
                return 'cuadrada'
            else:
                return 'triangular'  # Default
                
        except Exception:
            return 'triangular'  # Default
    
    def _extraer_figuras(self, imagen, tipo_secuencia: str) -> List[FiguraGeometrica]:
        """Extraer figuras individuales de la secuencia."""
        figuras = []
        
        try:
            gris = cv2.cvtColor(imagen, cv2.COLOR_BGR2GRAY)
            
            # Detectar puntos (círculos pequeños)
            circles = cv2.HoughCircles(
                gris, cv2.HOUGH_GRADIENT, 1, 20,
                param1=50, param2=30, minRadius=2, maxRadius=15
            )
            
            if circles is not None:
                circles = np.round(circles[0, :]).astype("int")
                
                # Agrupar puntos por regiones (figuras)
                grupos = self._agrupar_puntos_por_figura(circles)
                
                for i, grupo in enumerate(grupos):
                    figura = FiguraGeometrica(
                        posicion=i + 1,
                        tipo=tipo_secuencia,
                        puntos=len(grupo),
                        coordenadas=[(int(x), int(y)) for x, y in grupo]
                    )
                    figuras.append(figura)
            
            return figuras
            
        except Exception as e:
            print(f"Error extrayendo figuras: {e}")
            return []
    
    def _agrupar_puntos_por_figura(self, puntos) -> List[List[Tuple[int, int]]]:
        """Agrupar puntos que pertenecen a la misma figura."""
        if len(puntos) == 0:
            return []
        
        # Usar clustering simple basado en distancia
        grupos = []
        puntos_restantes = [(x, y) for x, y in puntos]
        
        while puntos_restantes:
            grupo_actual = [puntos_restantes.pop(0)]
            
            # Buscar puntos cercanos
            i = 0
            while i < len(puntos_restantes):
                punto = puntos_restantes[i]
                
                # Verificar si está cerca de algún punto del grupo actual
                cerca = False
                for px, py in grupo_actual:
                    distancia = np.sqrt((punto[0] - px)**2 + (punto[1] - py)**2)
                    if distancia < 100:  # Umbral de distancia
                        cerca = True
                        break
                
                if cerca:
                    grupo_actual.append(puntos_restantes.pop(i))
                else:
                    i += 1
            
            if len(grupo_actual) > 0:
                grupos.append(grupo_actual)
        
        # Ordenar grupos por posición X (izquierda a derecha)
        grupos.sort(key=lambda g: min(x for x, y in g))
        
        return grupos
    
    def _analizar_patron(self, figuras: List[FiguraGeometrica], tipo: str) -> Dict:
        """Analizar el patrón de la secuencia."""
        if not figuras:
            return {"error": "No hay figuras para analizar"}
        
        # Extraer secuencia de números de puntos
        secuencia = [f.puntos for f in figuras]
        
        # Analizar diferencias
        diferencias = []
        for i in range(1, len(secuencia)):
            diferencias.append(secuencia[i] - secuencia[i-1])
        
        # Analizar segundas diferencias
        segundas_diferencias = []
        for i in range(1, len(diferencias)):
            segundas_diferencias.append(diferencias[i] - diferencias[i-1])
        
        return {
            "secuencia": secuencia,
            "diferencias": diferencias,
            "segundas_diferencias": segundas_diferencias,
            "tipo_patron": self._clasificar_patron(secuencia, diferencias, segundas_diferencias),
            "es_aritmetica": len(set(diferencias)) <= 1 if diferencias else False,
            "es_cuadratica": len(set(segundas_diferencias)) <= 1 if segundas_diferencias else False
        }
    
    def _clasificar_patron(self, secuencia: List[int], dif1: List[int], dif2: List[int]) -> str:
        """Clasificar el tipo de patrón matemático."""
        
        # Verificar números triangulares: 1, 3, 6, 10, 15, ...
        triangulares = [n * (n + 1) // 2 for n in range(1, len(secuencia) + 1)]
        if secuencia == triangulares[:len(secuencia)]:
            return "numeros_triangulares"
        
        # Verificar números cuadrados: 1, 4, 9, 16, 25, ...
        cuadrados = [n * n for n in range(1, len(secuencia) + 1)]
        if secuencia == cuadrados[:len(secuencia)]:
            return "numeros_cuadrados"
        
        # Verificar progresión aritmética
        if dif1 and len(set(dif1)) <= 1:
            return "progresion_aritmetica"
        
        # Verificar progresión cuadrática
        if dif2 and len(set(dif2)) <= 1:
            return "progresion_cuadratica"
        
        return "patron_personalizado"
    
    def _extraer_datos_tabla(self, texto: str) -> Dict:
        """Extraer datos de tabla del texto."""
        datos = {}
        
        # Buscar tabla de posición y área/puntos
        patron_tabla = r'posición.*?(\d+).*?(\d+).*?(\d+).*?(\d+)'
        match = re.search(patron_tabla, texto, re.IGNORECASE | re.DOTALL)
        
        if match:
            datos["posiciones"] = [int(x) for x in match.groups()]
        
        # Buscar valores de área
        patron_area = r'área.*?(\d+).*?(\d+).*?(\d+).*?(\d+)'
        match_area = re.search(patron_area, texto, re.IGNORECASE | re.DOTALL)
        
        if match_area:
            datos["areas"] = [int(x) for x in match_area.groups()]
        
        return datos
    
    def _deducir_formula(self, patron: Dict, tipo: str) -> str:
        """Deducir la fórmula matemática de la secuencia."""
        
        tipo_patron = patron.get("tipo_patron", "")
        
        if tipo_patron == "numeros_triangulares":
            return "T(n) = n(n+1)/2"
        elif tipo_patron == "numeros_cuadrados":
            return "C(n) = n²"
        elif tipo_patron == "progresion_aritmetica":
            if patron.get("diferencias"):
                d = patron["diferencias"][0]
                a1 = patron["secuencia"][0] if patron["secuencia"] else 0
                return f"a(n) = {a1} + {d}(n-1)"
        elif tipo_patron == "progresion_cuadratica":
            # Fórmula general cuadrática: an² + bn + c
            return "a(n) = an² + bn + c"
        
        return "Fórmula no determinada"
    
    def _generar_predicciones(self, patron: Dict, tipo: str) -> Dict:
        """Generar predicciones para términos futuros."""
        
        secuencia = patron.get("secuencia", [])
        if not secuencia:
            return {}
        
        tipo_patron = patron.get("tipo_patron", "")
        predicciones = {}
        
        if tipo_patron == "numeros_triangulares":
            # Calcular términos 5-10
            for n in range(len(secuencia) + 1, 11):
                predicciones[f"posicion_{n}"] = n * (n + 1) // 2
        
        elif tipo_patron == "numeros_cuadrados":
            for n in range(len(secuencia) + 1, 11):
                predicciones[f"posicion_{n}"] = n * n
        
        elif tipo_patron == "progresion_aritmetica":
            if patron.get("diferencias"):
                d = patron["diferencias"][0]
                ultimo = secuencia[-1]
                for i in range(1, 6):
                    n = len(secuencia) + i
                    predicciones[f"posicion_{n}"] = ultimo + d * i
        
        return predicciones
    
    def _analizar_triangular(self, figuras: List[FiguraGeometrica]) -> Dict:
        """Análisis específico para números triangulares."""
        return {
            "tipo": "triangular",
            "formula": "T(n) = n(n+1)/2",
            "descripcion": "Números triangulares: puntos que forman triángulos equiláteros"
        }
    
    def _analizar_cuadrada(self, figuras: List[FiguraGeometrica]) -> Dict:
        """Análisis específico para números cuadrados."""
        return {
            "tipo": "cuadrada",
            "formula": "C(n) = n²",
            "descripcion": "Números cuadrados: puntos que forman cuadrados"
        }
    
    def _analizar_pentagonal(self, figuras: List[FiguraGeometrica]) -> Dict:
        """Análisis específico para números pentagonales."""
        return {
            "tipo": "pentagonal",
            "formula": "P(n) = n(3n-1)/2",
            "descripcion": "Números pentagonales: puntos que forman pentágonos"
        }
    
    def _analizar_fibonacci(self, figuras: List[FiguraGeometrica]) -> Dict:
        """Análisis específico para secuencia de Fibonacci."""
        return {
            "tipo": "fibonacci",
            "formula": "F(n) = F(n-1) + F(n-2)",
            "descripcion": "Secuencia de Fibonacci: cada término es suma de los dos anteriores"
        }

# Función de utilidad
def analizar_secuencia_imagen(imagen_path: str, texto: str = "") -> Dict:
    """Función de conveniencia para analizar secuencia en imagen."""
    analizador = AnalizadorSecuencias()
    return analizador.analizar_secuencia(imagen_path, texto)

if __name__ == "__main__":
    # Test básico
    import sys
    if len(sys.argv) > 1:
        resultado = analizar_secuencia_imagen(sys.argv[1])
        print(f"Análisis de secuencia: {resultado}")
