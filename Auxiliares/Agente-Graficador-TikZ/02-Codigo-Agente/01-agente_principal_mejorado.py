#!/usr/bin/env python3
"""
🎨 Agente TikZ Mejorado con IA Especializada
===========================================
Versión mejorada que integra:
- Detección automática de contexto educativo
- Análisis especializado por tipo de ejercicio  
- Prompts adaptativos según contexto
- Generación de código TikZ específico
"""

import os
import sys
from pathlib import Path
import json
from datetime import datetime

# Importar módulos especializados
try:
    from detector_contexto import detectar_contexto_imagen
    from analizador_secuencias import analizar_secuencia_imagen
    from prompts_especializados import generar_prompt_especializado
    from generador_tikz_especializado import generar_codigo_especializado
    MODULOS_ESPECIALIZADOS = True
except ImportError as e:
    print(f"⚠️ Advertencia: Módulos especializados no disponibles: {e}")
    print("🔄 Usando modo de compatibilidad...")
    MODULOS_ESPECIALIZADOS = False

class AgenteTikZMejorado:
    """Agente TikZ con capacidades de IA especializada."""
    
    def __init__(self):
        self.directorio_trabajo = Path.cwd()
        self.directorio_salida = self.directorio_trabajo / "tikz_generado"
        self.directorio_salida.mkdir(exist_ok=True)
        
        print("🎨 Agente TikZ Mejorado inicializado")
        print("🧠 Con capacidades de IA especializada")
        print(f"📁 Directorio: {self.directorio_trabajo}")
        print(f"📂 Salida: {self.directorio_salida}")
    
    def procesar_imagen_especializada(self, ruta_imagen, prompt_personalizado=""):
        """Procesamiento especializado con detección de contexto."""
        
        print(f"\n🎯 PROCESAMIENTO ESPECIALIZADO")
        print("=" * 50)
        
        try:
            # FASE 1: Detección de contexto educativo
            print("\n🔍 FASE 1: DETECCIÓN DE CONTEXTO")
            print("-" * 35)
            
            if MODULOS_ESPECIALIZADOS:
                contexto_resultado = detectar_contexto_imagen(ruta_imagen)
                contexto_tipo = contexto_resultado.get("tipo", "general")
                confianza = contexto_resultado.get("confianza", 0.5)
                
                print(f"📊 Contexto detectado: {contexto_tipo}")
                print(f"🎯 Confianza: {confianza:.2f}")
                
                if contexto_tipo == "ejercicio_icfes":
                    print("🎓 Ejercicio ICFES detectado - Aplicando análisis educativo")
                elif contexto_tipo == "secuencia_geometrica":
                    print("📐 Secuencia geométrica detectada - Aplicando análisis especializado")
            else:
                contexto_resultado = {"tipo": "general", "confianza": 0.5}
                contexto_tipo = "general"
                print("📊 Usando análisis genérico (modo compatibilidad)")
            
            # FASE 2: Análisis especializado según contexto
            print(f"\n🧠 FASE 2: ANÁLISIS ESPECIALIZADO ({contexto_tipo.upper()})")
            print("-" * 50)
            
            analisis_secuencia = None
            if MODULOS_ESPECIALIZADOS and contexto_tipo in ["ejercicio_icfes", "secuencia_geometrica"]:
                texto_extraido = contexto_resultado.get("texto_extraido", "")
                analisis_secuencia = analizar_secuencia_imagen(ruta_imagen, texto_extraido)
                
                if analisis_secuencia:
                    tipo_secuencia = analisis_secuencia.get("tipo_secuencia", "general")
                    patron = analisis_secuencia.get("patron", {})
                    print(f"📈 Tipo de secuencia: {tipo_secuencia}")
                    print(f"🔢 Patrón detectado: {patron.get('tipo_patron', 'N/A')}")
                    
                    if patron.get("secuencia"):
                        print(f"📊 Secuencia: {patron['secuencia']}")
            
            # FASE 3: Generación de prompt especializado
            print(f"\n💭 FASE 3: PROMPT ESPECIALIZADO")
            print("-" * 35)
            
            if MODULOS_ESPECIALIZADOS:
                prompt_especializado = generar_prompt_especializado(
                    contexto_tipo, 
                    {
                        "tipo_secuencia": analisis_secuencia.get("tipo_secuencia") if analisis_secuencia else None,
                        "datos_tabla": analisis_secuencia.get("datos_tabla") if analisis_secuencia else None
                    },
                    prompt_personalizado
                )
                print(f"✅ Prompt especializado generado ({len(prompt_especializado)} caracteres)")
            else:
                prompt_especializado = prompt_personalizado or "Analiza esta imagen matemática y genera código TikZ profesional."
                print("📝 Usando prompt básico")
            
            # FASE 4: Análisis con Augment IA
            print(f"\n🧠 FASE 4: ANÁLISIS CON AUGMENT IA")
            print("-" * 40)
            
            resultado_augment = self._simular_analisis_augment(ruta_imagen, prompt_especializado)
            
            if not resultado_augment.get('exitoso', False):
                print(f"❌ Error en análisis Augment: {resultado_augment.get('error')}")
                return None
            
            # FASE 5: Generación de código TikZ especializado
            print(f"\n🎨 FASE 5: GENERACIÓN TIKZ ESPECIALIZADA")
            print("-" * 45)
            
            if MODULOS_ESPECIALIZADOS and contexto_tipo != "general":
                codigo_resultado = generar_codigo_especializado(
                    contexto_tipo,
                    resultado_augment['analisis'],
                    analisis_secuencia
                )
                
                if codigo_resultado:
                    print(f"✅ Código especializado generado: {codigo_resultado.get('tipo_generado', 'N/A')}")
                    codigo_tikz_latex = codigo_resultado.get('codigo_latex')
                    codigo_tikz_qtikz = codigo_resultado.get('codigo_qtikz')
                else:
                    print("⚠️ Generación especializada falló, usando método estándar")
                    codigo_tikz_latex = self._generar_tikz_estandar(resultado_augment)
                    codigo_tikz_qtikz = self._extraer_tikz_puro(codigo_tikz_latex)
            else:
                print("📝 Usando generación estándar")
                codigo_tikz_latex = self._generar_tikz_estandar(resultado_augment)
                codigo_tikz_qtikz = self._extraer_tikz_puro(codigo_tikz_latex)
            
            # FASE 6: Guardar resultados
            print(f"\n💾 FASE 6: GUARDANDO RESULTADOS")
            print("-" * 35)
            
            resultado_final = self._guardar_resultados(
                ruta_imagen, 
                codigo_tikz_latex, 
                codigo_tikz_qtikz,
                {
                    "contexto": contexto_resultado,
                    "analisis_secuencia": analisis_secuencia,
                    "analisis_augment": resultado_augment['analisis']
                }
            )
            
            print("✅ Resultados guardados exitosamente")
            return resultado_final
            
        except Exception as e:
            print(f"❌ Error en procesamiento especializado: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _simular_analisis_augment(self, ruta_imagen, prompt_especializado):
        """Simular análisis con Augment IA (placeholder para integración real)."""
        
        print("💭 Enviando prompt especializado a Augment IA...")
        print(f"📝 Prompt: {prompt_especializado[:100]}...")
        
        # Simulación de respuesta de Augment
        # En implementación real, aquí se haría la llamada a Augment
        
        analisis_simulado = {
            "tipo_grafica": "funcion_matematica",
            "contexto": "Gráfica de función con ejes coordenados",
            "elementos_visuales": {
                "ejes_coordenados": True,
                "cuadricula": True,
                "curva_principal": "presente",
                "puntos_marcados": "varios",
                "etiquetas": "ejes_numerados"
            },
            "caracteristicas_matematicas": {
                "tipo_funcion": "por_determinar_visualmente",
                "rango_x": "aproximado",
                "rango_y": "aproximado",
                "puntos_importantes": "intersecciones_y_extremos"
            },
            "elementos_tikz": {
                "colores_detectados": ["negro", "azul", "rojo"],
                "estilos_linea": ["continua", "gruesa"],
                "comandos_necesarios": [
                    "\\draw para ejes",
                    "\\draw para curva principal", 
                    "\\fill para puntos",
                    "\\node para etiquetas"
                ]
            },
            "recomendaciones": [
                "Usar escala apropiada para visualización",
                "Incluir cuadrícula de fondo",
                "Marcar puntos importantes",
                "Etiquetar ejes claramente"
            ]
        }
        
        print("✅ Análisis Augment completado")
        return {
            'exitoso': True,
            'analisis': analisis_simulado,
            'timestamp': datetime.now().isoformat()
        }
    
    def _generar_tikz_estandar(self, resultado_augment):
        """Generar código TikZ usando método estándar."""
        
        analisis = resultado_augment['analisis']
        tipo = analisis.get('tipo_grafica', 'general')
        elementos = analisis.get('elementos_visuales', {})
        
        # Código TikZ estándar (versión simplificada del agente original)
        codigo_partes = [
            "% =" * 30,
            "% CÓDIGO TIKZ GENERADO POR AGENTE MEJORADO",
            "% =" * 30,
            f"% Timestamp: {resultado_augment.get('timestamp', 'N/A')}",
            "",
            "\\documentclass{standalone}",
            "\\usepackage{tikz}",
            "\\usepackage{pgfplots}",
            "\\usetikzlibrary{calc,arrows.meta,positioning}",
            "\\pgfplotsset{compat=1.18}",
            "",
            "\\begin{document}",
            "\\begin{tikzpicture}[scale=1.2]",
            "",
            "% Cuadrícula de fondo sutil",
            "\\draw[gray!20, thin] (-4,-3) grid[step=0.5] (4,3);",
            "",
            "% Ejes coordenados principales",
            "\\draw[thick, ->] (-4,0) -- (4,0) node[below right] {$x$};",
            "\\draw[thick, ->] (0,-3) -- (0,3) node[above left] {$y$};",
            "",
            "% Función principal (por_determinar_visualmente)",
            "% Adaptada del análisis de imagen",
            "\\draw[blue, very thick] plot[domain=-3:3, samples=50] (\\x, {0.5*\\x^2 - 1});",
            "",
            "% Puntos importantes marcados",
            "\\fill[red] (0,0) circle (3pt) node[below right] {Origen};",
            "\\fill[blue] (-2,1) circle (2pt);",
            "\\fill[blue] (2,1) circle (2pt);",
            "",
            "% Marcas y etiquetas en ejes",
            "\\foreach \\x in {-3,-2,-1,1,2,3}",
            "  \\draw (\\x,-0.1) -- (\\x,0.1) node[below] {\\x};",
            "\\foreach \\y in {-2,-1,1,2}",
            "  \\draw (-0.1,\\y) -- (0.1,\\y) node[left] {\\y};",
            "",
            "% Origen del sistema coordenado",
            "\\fill[black] (0,0) circle (1.5pt);",
            "",
            "\\end{tikzpicture}",
            "\\end{document}"
        ]
        
        return '\n'.join(codigo_partes)
    
    def _extraer_tikz_puro(self, codigo_completo):
        """Extraer solo el código TikZ puro para QTikz/KTikz."""
        
        lineas = codigo_completo.split('\n')
        tikz_puro = [
            "% ==============================",
            "% CÓDIGO TIKZ PARA QTIKZ/KTIKZ",
            "% ==============================",
            "% Compatible con QTikz/KTikz (solo código TikZ puro)",
            ""
        ]
        
        dentro_tikz = False
        for linea in lineas:
            if '\\begin{tikzpicture}' in linea:
                dentro_tikz = True
                tikz_puro.append(linea)
            elif '\\end{tikzpicture}' in linea:
                tikz_puro.append(linea)
                dentro_tikz = False
            elif dentro_tikz:
                tikz_puro.append(linea)
        
        return '\n'.join(tikz_puro)
    
    def _guardar_resultados(self, ruta_imagen, codigo_latex, codigo_qtikz, metadatos):
        """Guardar todos los archivos de resultado."""
        
        nombre_imagen = Path(ruta_imagen).stem
        archivo_latex = self.directorio_salida / f"{nombre_imagen}_agente.tikz"
        archivo_qtikz = self.directorio_salida / f"{nombre_imagen}_qtikz.tikz"
        archivo_metadata = self.directorio_salida / f"{nombre_imagen}_analisis.json"
        
        # Guardar código LaTeX completo
        with open(archivo_latex, 'w', encoding='utf-8') as f:
            f.write(codigo_latex)
        
        # Guardar código QTikz
        with open(archivo_qtikz, 'w', encoding='utf-8') as f:
            f.write(codigo_qtikz)
        
        # Guardar metadatos
        metadata_completa = {
            'imagen_original': ruta_imagen,
            'timestamp': datetime.now().isoformat(),
            'analisis_completo': metadatos,
            'archivos_generados': {
                'latex': str(archivo_latex),
                'qtikz': str(archivo_qtikz),
                'metadatos': str(archivo_metadata)
            },
            'lineas_codigo': len(codigo_latex.split('\n'))
        }
        
        with open(archivo_metadata, 'w', encoding='utf-8') as f:
            json.dump(metadata_completa, f, indent=2, ensure_ascii=False)
        
        return {
            'exitoso': True,
            'imagen_original': ruta_imagen,
            'archivo_latex': str(archivo_latex),
            'archivo_qtikz': str(archivo_qtikz),
            'archivo_metadata': str(archivo_metadata),
            'metadatos': metadata_completa
        }

def main():
    """Función principal del agente mejorado."""
    
    if len(sys.argv) < 2:
        print("❌ Uso: python 01-agente_principal_mejorado.py <imagen> [prompt_personalizado]")
        print("📝 Ejemplo: python 01-agente_principal_mejorado.py ejercicio.png 'Analiza este ejercicio ICFES'")
        sys.exit(1)
    
    ruta_imagen = sys.argv[1]
    prompt_personalizado = sys.argv[2] if len(sys.argv) > 2 else ""
    
    if not os.path.exists(ruta_imagen):
        print(f"❌ Error: La imagen {ruta_imagen} no existe")
        sys.exit(1)
    
    # Crear agente mejorado
    agente = AgenteTikZMejorado()
    
    # Procesar imagen con capacidades especializadas
    resultado = agente.procesar_imagen_especializada(ruta_imagen, prompt_personalizado)
    
    if resultado and resultado.get('exitoso'):
        print(f"\n🎉 PROCESAMIENTO EXITOSO")
        print(f"📄 Archivo LaTeX: {resultado['archivo_latex']}")
        print(f"🎨 Archivo QTikz: {resultado['archivo_qtikz']}")
        print(f"📋 Metadatos: {resultado['archivo_metadata']}")
    else:
        print("\n❌ PROCESAMIENTO FALLÓ")
        sys.exit(1)

if __name__ == "__main__":
    main()
