# 🎨 Agente TikZ Simplificado - Solo Augment + VSCode

## ✨ ¿Qué es?

El **Agente TikZ** es un sistema simplificado que usa **Augment IA dentro de VSCode** para analizar imágenes matemáticas y generar código TikZ profesional.

**NO requiere Python, entornos virtuales ni configuraciones complejas.**

## 🚀 Cómo Usar (Súper Simple)

### 1. Selecciona una imagen matemática
- Abre cualquier imagen (.png, .jpg, etc.) en VSCode
- Puede ser desde cualquier carpeta del proyecto

### 2. Ejecuta el Agente TikZ
- Presiona `Ctrl+Shift+P`
- Bus<PERSON> "Tasks: Run Task"
- Selecciona "🎨 Agente TikZ: Analizar Imagen Matemática"

### 3. Usa Augment IA
- El task te mostrará las instrucciones
- **Usa Augment IA** para analizar la imagen y generar código TikZ
- Augment creará el código automáticamente

## 🎯 Tasks Disponibles

| Task | Descripción |
|------|-------------|
| 🎨 **<PERSON><PERSON><PERSON>** | Análisis automático con Augment |
| 🎨 **Análisis Personalizado** | Con contexto específico (trigonometría, etc.) |
| 📋 **Compilar TikZ con LaTeX** | Compila archivos .tex |
| 🎨 **Abrir QTikz** | Visualiza código TikZ |
| 📁 **Abrir Templates TikZ** | Acceso a ejemplos |

## 📁 Estructura Limpia

```
Agente-Graficador-TikZ/
├── 01-Documentacion/          # Guías y tutoriales
├── 03-Configuracion-VSCode/   # Tasks.json
├── 04-Ejemplos-y-Pruebas/     # Ejemplos de uso
├── 05-Templates-TikZ/         # Plantillas TikZ
├── Laboratorio_Agente_TikZ/   # Resultados generados
└── README_AGENTE_TIKZ.md      # Esta guía
```

## ✅ Ventajas

- ✅ **Sin Python** - Solo Augment + VSCode
- ✅ **Sin entornos virtuales** - Funciona directamente
- ✅ **Desde cualquier carpeta** - Tasks globales
- ✅ **Integración perfecta** con tu flujo de trabajo
- ✅ **Resultados profesionales** - Código TikZ optimizado

## 🎨 Ejemplo de Uso

1. Tienes una imagen de un cilindro con dimensiones
2. Abres la imagen en VSCode
3. Ejecutas "🎨 Agente TikZ: Analizar Imagen Matemática"
4. Augment analiza y genera código TikZ del cilindro
5. Guardas el resultado en `/Lab-Manjaro/PuntoK-Rnw/`

**¡Así de simple! 🎉**

---

**El Agente TikZ ahora es puro Augment + VSCode. Sin complicaciones. 🚀**
