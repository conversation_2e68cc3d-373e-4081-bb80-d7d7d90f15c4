{"version": "2.0.0", "tasks": [{"label": "🎨 Agente TikZ v2.0: <PERSON><PERSON><PERSON> (Mejorado)", "type": "shell", "command": "python3", "args": ["${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/02-<PERSON><PERSON>-Agente/01-agente_principal_mejorado.py", "${file}"], "options": {"cwd": "${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/02-<PERSON><PERSON>-Agente"}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Analiza imagen con Agente TikZ v2.0 mejorado (detección automática de contexto)"}, {"label": "🎨 Agente TikZ v2.0: <PERSON><PERSON><PERSON>is Personalizado", "type": "shell", "command": "python3", "args": ["${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/02-<PERSON><PERSON>-Agente/01-agente_principal_mejorado.py", "${file}", "${input:prompt<PERSON><PERSON><PERSON><PERSON><PERSON>}"], "options": {"cwd": "${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/02-<PERSON><PERSON>-Agente"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Analiza imagen con prompt personalizado usando Agente TikZ v2.0"}, {"label": "🎨 Agente TikZ v1.0: An<PERSON><PERSON>is Clásico", "type": "shell", "command": "python3", "args": ["${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/02-<PERSON><PERSON>-Agente/01-agente_principal.py", "${file}", "${input:prompt<PERSON><PERSON><PERSON><PERSON><PERSON>}"], "options": {"cwd": "${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/02-<PERSON><PERSON>-Agente"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Analiza imagen con versión clásica del agente (compatibilidad)"}, {"label": "📋 Compilar TikZ con LaTeX", "type": "shell", "command": "pdflatex", "args": ["-interaction=nonstopmode", "${file}"], "options": {"cwd": "${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/02-<PERSON><PERSON>-Agente"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": ["$latex"], "detail": "Compila archivo TikZ/LaTeX seleccionado"}, {"label": "🔍 Ver Resultados TikZ Generados", "type": "shell", "command": "ls", "args": ["-la", "tikz_generado/"], "options": {"cwd": "${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/02-<PERSON><PERSON>-Agente"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Mostrar archivos generados en tikz_generado/"}, {"label": "🧪 Test Mejoras Agente v2.0", "type": "shell", "command": "python3", "args": ["10-test_mejoras.py"], "options": {"cwd": "${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/02-<PERSON><PERSON>-Agente"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Ejecutar tests de verificación de mejoras v2.0"}, {"label": "🎨 <PERSON><PERSON><PERSON> con Resultado", "type": "shell", "command": "qtikz", "args": ["tikz_generado/${fileBasenameNoExtension}_qtikz.tikz"], "options": {"cwd": "${workspaceFolder}/Auxiliares/Agente-Graficador-TikZ/02-<PERSON><PERSON>-Agente"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Abrir resultado en QTikz para visualización"}], "inputs": [{"id": "prompt<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Prompt personalizado para análisis con Augment IA", "default": "Analiza este ejercicio ICFES sobre secuencias geométricas con máxima precisión educativa", "type": "promptString"}]}